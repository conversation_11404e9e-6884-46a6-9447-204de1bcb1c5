# 游戏需求文档: AI Text Game "I am NPC"

**文档说明:** 本文档是项目当前阶段的权威需求说明，整合并细化了所有核心设计。本文档将作为后续开发、测试和迭代的最终依据。

---

## 前置条件与技术约束

### 用户系统集成
- **外部IDP系统**: 游戏采用外部身份提供商(Identity Provider)系统进行用户认证和授权
- **用户数据存储**: 游戏内只保存用户的业务凭证和游戏相关属性，不存储敏感的身份信息
- **认证流程**: 支持OAuth 2.0/OpenID Connect等标准协议

### 技术栈约束
- **后端语言**: 使用Go语言实现后端服务，充分利用其高并发和异步处理能力
- **AI集成方式**: AI能力通过HTTP接口调用实现，采用异步请求处理模式
- **数据库管理**: 必须支持数据库迁移功能，每次数据结构变更都需要创建迁移脚本
- **API响应格式**: 使用标准HTTP状态码，简化响应体结构

### 基础设施服务前置条件
- **API网关**: 统一的API入口，负责路由、认证、限流和监控
- **PostgreSQL数据库**: 主要的关系型数据库，用于持久化存储游戏数据
- **Redis缓存服务**: 内存缓存和消息队列，用于提升性能和异步处理
- **文件存储服务**: 对象存储服务，用于存储游戏资源和用户上传内容
- **负载均衡器**: 分发请求到多个服务实例，保证高可用性

### 系统架构原则
- **微服务架构**: 采用微服务架构，支持独立部署和扩展
- **异步处理**: 重度依赖异步处理能力，特别是AI接口调用
- **数据一致性**: 通过事务和分布式锁保证数据一致性
- **可扩展性**: 支持水平扩展和负载均衡
- **统一响应格式**: 所有API响应采用统一的JSON格式，不直接返回HTTP状态码作为业务状态


### 生成结构化文本接口说明

所有需要 AI 生成结构化输出的功能，都依赖于以下接口。这是整个游戏世界能够精确、稳定演化的技术基础。

- **Endpoint**: `POST /api/w/my-workspace/jobs/run/p/f/gemini/js_structured_output`
- **URL**: `https://wm.atjog.com/api/w/my-workspace/jobs/run/p/f/gemini/js_structured_output`
- **参数**:
  - `model` (string, 必填): 模型名称。
  - `prompt` (string, 必填): 主提示词，包含具体的场景、角色、意图等上下文信息。
  - `system_instruction` (string, 可选): 系统指令，用于设定 AI 的角色、输出风格等全局规则。
  - `responseSchema` (JSON, 必填): 定义了期望 AI 返回的 JSON 对象的具体格式和规范。

该接口采用异步逻辑, 参考下面的异步示例:

```
TOKEN=''
BODY='{}'
URL='https://wm.atjog.com/api/w/my-workspace/jobs/run/p/f/gemini/js_structured_output'
UUID=$(curl -s -H 'Content-Type: application/json' -H "Authorization: Bearer $TOKEN" -X POST -d "$BODY" $URL)


URL="https://wm.atjog.com/api/w/my-workspace/jobs_u/completed/get_result_maybe/$UUID"
while true; do
  curl -s -H "Authorization: Bearer $TOKEN" $URL -o res.json
  COMPLETED=$(cat res.json | jq .completed)
  if [ "$COMPLETED" = "true" ]; then
    cat res.json | jq .result
    break
  else
    sleep 1
  fi
done
```

如果需要测试接口, 请使用 mock 接口方式进行测试:

---

## 游戏概述

### 游戏愿景
打造一款由AI文本生成接口驱动的、高度动态化的文本冒险沙盒游戏。在游戏中，玩家将扮演一个与世界中其他 NPC 地位完全平等的普通角色。整个游戏世界，包括所有角色、物品、场景和事件，都由AI文本生成服务根据统一的内在逻辑和时间流逝进行动态推演，提供极致的沉浸感、自由度和重复可玩性。

### 核心特色
- **结构化的AI推演**: AI文本生成接口的核心输出是可被机器精确解析的指令集 (JSON)，确保了世界演化的精确性、稳定性和可追溯性。
- **目标驱动的叙事**: 世界的长期发展由一个或多个“目标”实体驱动，为开放的沙盒世界提供了宏观方向和内在冲突。
- **沉浸式前端体验**: 专为文本阅读和交互优化的多面板界面，在保证信息清晰度的同时，营造出强烈的游戏氛围。
- **异步处理架构**: 基于Go语言的高并发异步处理能力，确保AI接口调用不阻塞用户体验。
- **场景中心的世界结构**: 游戏世界由一系列具有内在属性的“场景”构成。每个场景都是一个独立的容器，封装了其中的角色、物品和状态。
- **批量演化循环**: 游戏的核心演化基于固定的时间心跳 (Tick)，对所有发生变化的场景进行批量处理，保证了处理效率和世界状态的一致性。
- **统一的实体生态**: 游戏中的所有事物——无论是角色、物品、技能、目标还是天气——都基于统一的“实体-特质”模型，并支持“集体实体”以表现庞大群体。
- **动态记忆与遗忘**: 角色拥有一个动态的记忆系统，能记住关键事件，并随着时间推移而遗忘，模拟真实生物的记忆规律。
- **平等的角色生态**: 玩家角色与 NPC 地位平等，遵循相同的游戏规则和物理法则。
- **基于特质的判定**: 所有交互结果均由 AI 基于参与方的自然语言“特质”进行逻辑推导，取代传统数值计算。
- **情境驱动的开局**: 游戏根据玩家提供的高层概念，自动生成一个动态的“初始情境”，而非静态的开局。

---

## 核心玩法循环

游戏的核心循环由四个步骤构成，不断往复，推动游戏进程：

- **意图 (Intend)**: 玩家通过前端界面，以文本指令或点击按钮的形式，向后端提交行动意图。
- **演化 (Evolve)**: 后端服务器在一个时间心跳 (Tick) 内收集所有玩家和 NPC 的意图，并将相关的场景信息打包，批量发送给 AI 进行推演。
- **应用 (Apply)**: AI 返回结构化的指令集。后端接收并解析这些指令，然后严格按照指令顺序更新数据库中的游戏状态。
- **观察 (Observe)**: 前端从后端获取最新的游戏状态，收到“叙事文本”，并刷新界面。玩家观察到新的世界状态后，构思下一步行动。

---

## 核心后端系统

### 场景系统
- **职责**: 管理游戏世界的所有地点和空间，是世界的基本组成单位和逻辑容器。
- **核心需求**:
    - **场景作为容器**: 每个场景都是一个独立的对象，封装了其内部的角色、物品和事件。场景具有唯一标识符、名称、描述和特质列表。
    - **方向性与入口特性**: 场景的描述和特质应包含方向性信息，使得从不同方向进入会产生不同的结果。AI 在判定时必须将入口方向作为关键上下文。
    - **场景内容列表**: 每个场景内部都维护着一个动态的 `entities_present` 列表，记录当前场景中所有实体（角色、物品等）的 ID。
    - **场景关联性**: 每个场景都会保持一个关联场景的列表，用于指示场景之间的连接关系。支持单向和双向连接。
    - **场景状态管理**: 场景可以有不同的状态（如正常、危险、封闭），状态变化会影响其中的交互和事件。
    - **环境因素**: 场景应包含环境信息（天气、光照、温度等），这些因素会影响角色的行为和事件的发生。

#### 玩家视野 (Player's Field of View)
- **职责**: 管理玩家对游戏世界的认知范围，实现渐进式世界探索。
- **核心需求**:
    - **按需生成**: 游戏世界不在初始时完全生成。仅当玩家接近未知区域时，AI 才会按需生成新的场景。
    - **初始视野**: 游戏开局时，AI 会根据初始情境，生成玩家角色周边的有限数量（例如 3-5 个）的关联场景，构成玩家的初始“视野”。
    - **视野扩展**: 当玩家移动到一个视野边缘的场景时，系统会自动触发周边未知场景的生成，从而将新的场景纳入玩家的“已知世界地图”中。
    - **前端呈现**: 前端界面（如地图视图）只应显示玩家已经发现的场景及其连接关系，未知区域则保持模糊或隐藏，以营造探索感。

#### 多玩家世界初始化策略
- **世界规模预估**: 在世界创建时，AI接口需要根据世界描述生成一个**概念性的世界规模框架**，包括：
    - **主要地理区域**: 如"北方山脉"、"中央平原"、"南方沙漠"等大区域概念
    - **重要地标**: 主要城市、关键地点的大致位置关系
    - **距离概念**: 各区域间的相对距离和旅行时间估算
    - **世界边界**: 世界的大致边界和可探索范围
- **多玩家分散初始化**: 当多个玩家加入同一世界时：
    - **区域分配**: 根据世界规模框架，将玩家分配到不同的主要区域
    - **距离保证**: 确保玩家初始位置间至少有2-3天的游戏内旅行距离
    - **独立起点**: 每个玩家获得独立的初始场景群，避免直接冲突
    - **汇聚机制**: 设计自然的汇聚点（如中央城市、重要事件）供玩家后期相遇
- **动态调整**: 随着玩家探索，系统可以动态调整未生成区域的规模和内容，保持世界的一致性

### 角色系统
- **职责**: 管理玩家和 NPC 的所有状态和行为逻辑。
- **核心概念**:
    - **统一角色模型**: 玩家和 NPC 在数据结构和行为逻辑上完全一致。
    - **集体实体**: 支持用单一实例来表现大量相似的角色（如“一支军队”）。
#### 记忆系统
- **动态记忆存储**: 角色拥有动态的记忆存储，记录重要的事件、遭遇和信息
- **记忆衰减机制**: 记忆会根据重要性和时间流逝而逐渐遗忘或模糊
- **智能记忆提取**: 在与AI交互时，系统会智能提取与当前上下文最相关的记忆和近期记忆进行组合
- **记忆分类**: 记忆按类型分类存储（事件记忆、人物记忆、地点记忆、物品记忆等）
- **记忆关联**: 相关记忆之间建立关联链接，形成记忆网络

#### 阅历系统
- **经验积累**: 每次经历各种事件、了解NPC、发现物品等都会积累为阅历
- **阅历分类**:
  - **战斗阅历**: 与不同类型敌人的战斗经验，提升战斗判断能力
  - **社交阅历**: 与各种NPC的交往经验，提升社交互动效果
  - **探索阅历**: 对不同环境和地点的探索经验，提升发现隐藏内容的能力
  - **制作阅历**: 物品制作和使用的经验，提升制作成功率和效果
  - **事件阅历**: 处理各种突发事件的经验，提升应对类似情况的能力
- **阅历应用**: 在面对类似情况时，阅历会影响AI的判断和角色的行为选择
- **阅历传承**: 部分阅历可以通过教学、书籍等方式在角色间传递
- **任务与成就**: 基于阅历系统的任务完成和成就解锁机制
- **声誉与关系**: 社交阅历影响角色间的关系发展和声誉建立
- **技能与能力**: 各类阅历的积累会转化为相应的技能和特殊能力

#### 角色成长与行为
- **角色成长**: 角色的成长表现为**特质的变更**和**阅历的积累**。当角色完成特定挑战、使用特定物品或经历特定事件后，AI可以通过指令集移除旧特质、添加新特质，同时增加相应的阅历。例如，一个`胆小的`特质可能在角色战胜强大敌人后被移除，并添加`勇敢的`特质，同时获得"战胜强敌"的战斗阅历。
- **角色行为驱动**:
    - **自主行为**: NPC具有基于其特质、阅历和目标的自主行为模式，即使玩家不在场也会执行相应行动。
    - **反应行为**: 角色会根据环境变化、其他角色的行为和玩家的行动做出相应反应，阅历会影响反应的智能程度。
    - **学习能力**: 角色可以从经历中学习，通过阅历系统积累经验，调整其行为模式和决策倾向。

### 实体与特质系统
- **职责**: 提供游戏中所有事物的统一数据模型。
- **核心概念**:
    - **实体**: 游戏世界中的一切非生命事物皆为实体（物品、事件、天气等）。
    - **特质**: 依附于实体上的、描述其属性和行为的自然语言标签。
    - **目标实体**: 一种特殊的事件实体，用于驱动世界的长期叙事。
- **物品循环**: 物品作为一种实体，应形成一个完整的生命周期。例如，角色可以通过 `探索` 指令发现 `木材`，通过 `制作` 指令将 `木材` 变为 `木棍`，在战斗中 `木棍` 可能被消耗或损坏，其特质会发生改变，最终可能变为 `折断的木棍`。

#### 事件系统增强
- **NPC主动事件创建**: NPC具备主动创建和触发事件的能力，不仅仅是被动响应玩家行动
  - **陷阱设置**: NPC可以在特定地点设置陷阱，如"在森林小径设置绳索陷阱"
  - **伏击计划**: NPC可以制定伏击计划，在玩家必经之路等待
  - **资源争夺**: NPC可以主动争夺稀缺资源，创建竞争事件
  - **社交事件**: NPC可以发起聚会、会议、仪式等社交活动
- **环境事件**: 持续性的环境变化作为特殊事件类型
  - **天气变化**: 晴雨、风暴、雾霾等天气作为持续性事件影响游戏世界
  - **季节更替**: 春夏秋冬的季节变化事件，影响环境和角色行为
  - **自然灾害**: 地震、洪水、干旱等灾害事件
  - **环境演化**: 森林生长、河流改道、建筑老化等长期环境变化事件
- **事件传播机制**: 事件可以在NPC之间传播，形成连锁反应
- **延迟事件**: NPC可以创建延迟触发的事件，增加世界的不可预测性
- **事件优先级**: 不同类型的事件具有不同的优先级，影响AI处理顺序
- **事件持续性**: 区分瞬时事件和持续性事件，持续性事件会在多个时间周期内产生影响

### 游戏时间与世界演化系统
- **职责**: 驱动游戏世界的时间流逝和逻辑演化。
- **核心概念**:
    - **时间驱动演化**: 玩家消耗时间的操作是驱动世界状态变化的主要原因。
    - **批量演化**: 服务器以固定的时间心跳（Tick），对所有发生变化的场景进行并行的批量处理。
    - **可配置速率**: 允许设置游戏时间相对于现实时间的流逝速率，甚至可以分时段设置不同速率（例如：现实白天，游戏时间加速, 避免玩家过多的等待）。

### AI 交互与指令系统
- **职责**: 格式化与AI文本生成接口的通信，并解析执行其返回的指令。
- **核心概念**: 后端通过 Prompt Engineering 引导AI接口，AI的返回必须是格式严格的、包含 `narrative` 和 `state_changes` 的 JSON。后端拥有一个指令解析器，用于精确执行 `state_changes` 列表中的原子化操作。

### 输入内容校验系统
- **职责**: 对所有用户输入和AI生成内容进行安全性和合规性检查，确保游戏环境健康。
- **核心功能**:
    - **用户输入过滤**: 对玩家的文本指令、世界描述、角色设定等输入进行实时检查
        - **广告推销检测**: 识别和过滤商业广告、推销信息、外部链接等内容
        - **有害内容过滤**: 检测暴力、色情、仇恨言论、政治敏感等不当内容
        - **垃圾信息拦截**: 过滤重复刷屏、无意义字符串、恶意测试等垃圾内容
        - **长度和格式验证**: 确保输入符合预设的长度限制和格式要求
    - **AI内容审核**: 对AI生成的叙事文本、角色对话、事件描述进行后处理检查
        - **内容安全扫描**: 确保AI生成内容不包含不当信息
        - **逻辑一致性检查**: 验证生成内容与游戏世界设定的一致性
        - **质量评估**: 检查生成内容的可读性和游戏体验质量
    - **实时监控与告警**: 建立内容安全监控机制
        - **异常行为检测**: 识别异常的输入模式和可疑用户行为
        - **自动处理机制**: 对违规内容进行自动屏蔽、替换或标记
        - **人工审核流程**: 对争议内容提供人工复审机制
- **技术实现**:
    - **多层过滤策略**: 结合关键词过滤、机器学习模型、规则引擎等多种技术
    - **白名单机制**: 对游戏相关的专业术语和正常表达建立白名单
    - **用户信誉系统**: 根据用户历史行为调整检查严格程度
    - **性能优化**: 确保内容检查不影响游戏响应速度

### 数据库迁移系统
- **职责**: 管理数据库结构的版本控制和迁移。
- **核心功能**:
  - **迁移脚本管理**: 每次数据结构变更都创建对应的迁移脚本
  - **版本控制**: 数据库schema版本的跟踪和管理
  - **回滚机制**: 支持迁移的回滚操作
  - **数据完整性检查**: 迁移前后的数据完整性验证
  - **批量迁移**: 支持多个迁移脚本的批量执行

---

## 游戏内容与体验

### 新手引导 (Onboarding)
- **目的**: 帮助新玩家快速理解核心概念和操作方式。
- **实现**: 游戏开始时，应通过一个简短的、由 AI 驱动的引导任务来教学。例如，一个“被困在房间里”的初始情境，引导玩家学习 `观察` 周围环境、`拾取` 钥匙、`使用` 钥匙开门等基本指令，并在过程中解释“特质”和“意图”的概念。

### 交互示例（延迟反馈优化）
- **战斗**: 玩家意图`攻击哥布林`后，系统立即显示"你准备攻击哥布林..."，战斗结果通过延迟处理返回。AI综合玩家的特质(`身手敏捷`, `手持锋利的匕首`)、哥布林的特质(`反应迟钝`, `装备简陋的盔甲`)和相关阅历，在下一个时间周期返回详细的战斗结果和叙事。
- **社交**: 玩家意图`说服守卫让我进城`后，系统显示"你开始与守卫交谈..."，说服结果基于玩家的社交阅历、特质(`口齿伶俐`, `声名狼藉`)、守卫特质(`忠心耿耿`, `贪婪`)以及记忆中的关系历史，通过延迟反馈提供详细的对话结果。
- **探索**: 玩家在`黑暗的`洞穴中意图`探索`后，系统显示"你开始仔细搜索周围..."，探索结果基于玩家的探索阅历、特质(`携带火把`)等因素，延迟返回发现的物品、隐藏通道或遭遇的危险。
- **移动**: 玩家意图`向北移动`后立即更新位置，但对新场景的详细描述和可能遭遇的事件通过延迟处理提供，基于角色的探索阅历和环境特质生成个性化的场景描述。
- **沟通**:
  - **信件系统**: 大部分深度交流通过信件方式进行，玩家发送信件后可以继续其他活动，NPC的回信会在适当时间后到达
  - **留言板**: 在酒馆、市场等公共场所设置留言板，玩家可以发布和查看消息
  - **传话系统**: 通过NPC传递消息，增加信息传播的真实感和延迟感

### 社会交换系统（非量化经济替代）
- **人情债务系统**: 用自然语言描述的人情关系替代货币，如"欠铁匠一个人情"、"村长对你有恩"
- **以物易物**: 直接的物品交换，基于物品的实用性、稀缺性和角色需求进行判定
- **服务交换**: 用技能、劳动、信息等无形资产进行交换，如"用修理技能换取住宿"
- **声誉货币**: 基于角色在不同群体中的声誉进行交易，如"英雄声誉"、"工匠名望"
- **互助网络**: 建立基于信任和互助的社区关系，通过帮助他人获得未来的支持
- **制作与传承**:
  - **配方传承**: 制作知识通过师傅传授、古籍学习等方式获得，体现知识的价值
  - **材料获取**: 通过探索、交换、委托等非货币方式获得制作材料
  - **工艺品价值**: 物品价值基于制作者的技艺、材料稀缺性和文化意义，而非数值

### 文本游戏特有的叙事机制

#### 动态叙事生成
- **情境感知叙事**: AI根据当前环境、天气、时间、角色状态等因素，生成符合情境的叙事文本。
- **个性化描述**: 基于玩家角色的特质和经历，同一场景会产生不同的描述视角。
- **情感色彩**: 叙事文本应反映角色的情感状态，如恐惧时的描述更加紧张，快乐时更加生动。

#### 分支叙事与选择
- **多重结局**: 重要事件应提供多个选择分支，每个选择都会影响后续故事发展。
- **延迟后果**: 某些选择的后果可能在很久之后才显现，增加决策的深度。
- **道德选择**: 设计具有道德复杂性的选择，没有绝对的对错，增加游戏的思考深度。

#### 世界一致性维护
- **事件记录**: 系统需要维护详细的事件历史，确保AI生成的内容与之前的事件保持一致。
- **角色关系网**: 维护角色之间的关系图谱，确保NPC的行为符合其与玩家的关系历史。
- **世界状态同步**: 确保世界中的变化（如建筑被毁、NPC死亡）在所有相关场景中都得到体现。

### 游戏节奏与时间管理

#### 时间消耗机制
- **行动时间成本**: 不同行动消耗不同的游戏时间，如：
  - 观察环境：5-10分钟
  - 短距离移动：15-30分钟
  - 长距离旅行：数小时到数天
  - 深度交谈：30分钟-2小时
  - 战斗：10分钟-1小时
- **等待机制**: 玩家可以选择等待特定时间，观察世界的自然演化。
- **时间敏感事件**: 某些事件有时间限制，错过后会产生不同的后果。

#### 游戏节奏控制
- **紧张与缓解**: 通过事件的紧急程度和时间压力来调节游戏节奏。
- **探索与行动平衡**: 在自由探索和目标导向的行动之间保持平衡。
- **休息与恢复**: 提供角色休息和恢复的机制，避免持续的高强度体验。

### 社交与多人要素

#### 异步社交
- **留言系统**: 玩家可以在场景中留下消息，供其他玩家发现。
- **世界影响**: 一个玩家的重大行动可能影响其他玩家的世界状态。
- **传说系统**: 玩家的英雄事迹可能成为其他世界中的传说故事。

#### 协作机制
- **共享世界**: 多个玩家可以在同一个世界中进行游戏。
- **间接协作**: 玩家的行动为其他玩家创造机会或挑战。
- **知识共享**: 玩家发现的信息和地图可以与其他玩家分享。

---

## 前端用户界面与体验

### 核心设计原则
- **沉浸感优先**: 所有 UI 设计都应服务于将玩家的注意力完全吸引到游戏世界中，减少界面本身的存在感。
- **信息清晰**: 玩家需要随时能清晰、便捷地获取其角色状态和环境信息，但这些信息不应干扰主叙事。
- **易于上手，精于操作**: 为新手提供易于理解的点击式交互，同时为核心玩家保留高效的文本指令输入。

### 界面布局
- **多面板布局**: 界面采用多面板布局，将不同类型的信息在屏幕上进行区域划分。
    - **主叙事面板**: 占据屏幕主要区域，用于展示 AI 生成的故事描述。此面板应支持向上滚动以回顾历史信息。
    - **状态面板**: 固定在屏幕一侧或顶部/底部，始终显示角色的核心状态，如：当前时间、地点、以及最重要的几个正面/负面特质。
    - **互动面板**: 位于屏幕底部，包含文本输入框和常用动作的快捷按钮。
    - **可切换侧边栏**: 一个可按需显示/隐藏的侧边栏，包含多个标签页，用于展示更详细的信息，如：
        - **物品栏 (Inventory)**: 以列表形式展示角色拥有的所有物品。
        - **记忆日志 (Journal)**: 按时间倒序展示角色未遗忘的重要记忆。
        - **目标 (Objectives)**: 展示当前世界的主要目标及其状态。
        - **地图 (Map)**: (可选) 以图形或文本形式展示玩家已探索的区域。

### 交互模型
- **混合输入**: 同时支持两种输入方式：
    - **文本指令**: 玩家可以在输入框中键入完整的指令 (如 `> 拿起桌上的钥匙`)。
    - **快捷按钮**: 为最常用的动作（如“观察”、“等待”、“查看物品栏”）提供永久可见的按钮。
- **上下文感知点击**: 叙事文本中的关键实体（如“一扇门”、“哥布林”）应自动高亮为可点击链接，点击后自动在输入框填入相关指令（如 `> 调查 门`）。

### 核心用户用例 (User Cases)

按照玩家从进入游戏到创建世界，再到探索世界的自然流程组织：

#### 用例1: 首次进入游戏和身份验证
- **初始状态**: 玩家首次打开游戏，看到欢迎界面和登录选项。
- **玩家操作**: 玩家选择通过外部IDP系统进行身份验证。
- **前端行为**:
    - 跳转到外部身份提供商的登录页面（如Google、GitHub等）。
    - 处理OAuth回调，获取用户身份信息。
    - 显示主菜单界面，包含【创建新世界】、【我的世界】、【公共世界】、【加入世界】等选项。
- **用户引导**: 如果是新用户，突出显示【创建新世界】按钮并提供简单的引导说明。

#### 用例2: 创建新世界
- **初始状态**: 玩家位于“酒馆”场景。互动面板的文本输入框为空，快捷按钮包含【观察】、【等待】、【移动】。
- **玩家操作**: 玩家点击【观察】按钮。
- **前端行为**:
    - 前端向后端发送 `{"intent": "observe"}` 请求。
    - 在等待后端响应时，【观察】按钮可变为加载中状态，防止重复点击。
- **后端响应**: 后端返回新的叙事文本和更新后的状态。
- **前端行为**:
    - 主叙事面板追加新的描述：“你环顾四周，看到一个穿着斗篷的神秘人坐在角落里。”
    - 叙事文本中的“神秘人”被自动高亮为可点击链接。

#### 用例2: 上下文感知交互
-  **初始状态**: 如用例1结束时，主叙事面板显示了“神秘人”。
-  **玩家操作**: 玩家点击高亮显示的“神秘人”文本。
-  **前端行为**:
    -   互动面板的文本输入框中自动填入 `> 与 神秘人 交谈`。
    -   输入框自动获得焦点，光标位于末尾，允许玩家修改或直接提交。
-  **玩家操作**: 玩家按下回车键提交指令。
-  **前端行为**:
    -   前端向后端发送 `{"intent": "与 神秘人 交谈"}` 请求。
    -   主叙事面板追加玩家的指令行 `> 与 神秘人 交谈`。
    -   等待后端响应...

#### 用例3: 查看物品与记忆
-  **初始状态**: 游戏进行中，玩家获得了一些物品和记忆。
-  **玩家操作**: 玩家点击屏幕边缘的【侧边栏】图标。
-  **前端行为**:
    -   侧边栏从屏幕边缘滑出，默认显示【物品栏】标签页。
    -   【物品栏】中以列表形式展示玩家拥有的每个物品的名称和关键特质。
-  **玩家操作**: 玩家点击侧边栏顶部的【记忆日志】标签。
-  **前端行为**:
    -   侧边栏内容切换到记忆日志。
    -   按时间倒序列出玩家所有未遗忘的记忆，每条记忆包含发生时间和事件总结。
-  **玩家操作**: 玩家再次点击【侧边栏】图标或主叙事面板区域。
-  **前端行为**: 侧边栏收回。

#### 用例4: 高级文本指令
-  **初始状态**: 玩家位于“市场”场景，物品栏中有一个“银币”物品。
-  **玩家操作**: 玩家在互动面板的文本输入框中，手动输入 `> 用 1 个银币购买苹果`。
-  **前端行为**:
    -   前端向后端发送 `{"intent": "用 1 个银币购买苹果"}` 请求。
    -   主叙事面板追加玩家的指令行 `> 用 1 个银币购买苹果`。
-  **后端响应**: 后端返回交易成功或失败的叙事文本和状态更新（如物品栏中银币减少，苹果增加）。
-  **前端行为**: 刷新主叙事面板和状态面板。

- **初始状态**: 玩家在主菜单选择【创建新世界】。前端显示世界创建界面。
- **玩家操作**: 玩家在文本输入框中输入世界描述，例如："一个中世纪魔法世界，充满了巨龙和骑士"。
- **前端行为**:
    - 验证输入内容不为空且符合长度限制（如50-500字符）。
    - 进行内容安全检查，过滤不当内容。
    - 显示加载状态，提示"AI正在为您创建世界..."。
    - 向后端发送 `{"action": "create_world", "description": "一个中世纪魔法世界，充满了巨龙和骑士"}` 请求。
- **后端处理**:
    - 调用AI接口生成世界的基础结构（世界观、概念性地图框架、初始场景、玩家角色等）。
    - 生成世界规模预估和主要区域分布。
    - 将生成的世界数据保存到数据库。
    - 返回世界ID和基本信息。
- **前端行为**:
    - 接收到世界创建成功的响应后，自动跳转到游戏主界面。
    - 显示欢迎信息和世界的初始描述。
    - 玩家可以开始进行第一个行动。

#### 用例3: 世界列表和选择世界

- **初始状态**: 玩家在主菜单界面，可以看到【我的世界】、【公共世界】、【加入世界】等选项。
- **查看世界列表**:
    - **玩家操作**: 点击【我的世界】标签。
    - **前端行为**: 向后端请求玩家创建或参与过的世界列表。
    - **显示内容**: 以卡片形式展示每个世界，包含：
        - 世界名称和简短描述
        - 创建时间和最后游戏时间
        - 当前游戏进度（如"第3天，位于森林深处"）
        - 世界状态（活跃/暂停/已完成）
- **分享世界**:
    - **玩家操作**: 在某个世界卡片上点击【分享】按钮。
    - **前端行为**:
        - 弹出分享对话框，显示世界的分享码或链接。
        - 提供复制到剪贴板的功能。
        - 可选择分享权限（只读观察/允许加入游戏）。
- **加入分享的世界**:
    - **玩家操作**: 点击【加入世界】，输入分享码或链接。
    - **前端行为**: 验证分享码的有效性，显示世界预览信息。
    - **确认加入**: 玩家确认后，系统为其在该世界中创建角色，根据世界规模框架分配到合适的初始区域。
- **进入选择的世界**:
    - **玩家操作**: 在世界列表中点击某个世界的【进入游戏】按钮。
    - **前端行为**:
        - 显示加载界面，提示"正在加载世界..."。
        - 向后端请求该世界的当前状态和玩家角色信息。
        - 跳转到游戏主界面，恢复玩家在该世界中的游戏状态。
        - 在主叙事面板显示"欢迎回到[世界名称]"和当前场景描述。

#### 用例4: 探索新场景

### 视觉与表现
- **排版与可读性**: 必须选用高对比度、适合长时间阅读的字体和配色方案。
- **氛围营造**: 可以通过 subtle (微妙的) 的背景色或极简的背景图案来暗示当前场景的氛围（例如，在森林中是深绿色，在危险区域是淡红色）。
- **视觉提示**: 使用不同颜色或图标来区分不同类型的信息，例如 NPC 的对话、系统提示、战斗信息等。

### 技术要求
- **Web 应用**: 前端应作为一个单页应用 (SPA) 来构建。
- **响应式设计**: 界面必须能自适应桌面和移动设备的屏幕尺寸。
- **API 通信**: 前端通过 HTTP 请求与后端 API 进行通信，发送玩家指令并接收更新后的游戏状态。
- **状态管理**: 推荐使用成熟的前端状态管理库（如 Redux, Vuex, Pinia）来管理复杂的客户端状态。

### 前端性能与用户体验需求

#### 性能优化需求
- **加载性能**:
  - 首屏加载时间不超过3秒
  - 使用代码分割和懒加载优化资源加载
  - 实现渐进式加载，优先显示核心游戏界面
- **运行时性能**:
  - 界面响应时间不超过100ms
  - 长文本渲染优化，支持虚拟滚动
  - 内存使用控制，避免内存泄漏
- **网络优化**:
  - 实现请求缓存和去重
  - 支持离线模式下的基本功能
  - 使用WebSocket或Server-Sent Events实现实时更新

#### 用户体验增强
- **加载状态管理**:
  - 为所有异步操作提供明确的加载指示
  - 实现骨架屏或进度条，避免白屏等待
  - 支持操作取消和重试机制
- **错误处理**:
  - 友好的错误提示信息
  - 网络错误时的自动重连机制
  - 提供错误报告功能供调试使用
- **可访问性 (Accessibility)**:
  - 支持键盘导航
  - 提供屏幕阅读器支持
  - 高对比度模式支持
  - 字体大小可调节

#### 移动端特殊需求
- **触摸优化**:
  - 适配触摸手势操作
  - 优化按钮和链接的点击区域大小
  - 支持滑动和长按操作
- **屏幕适配**:
  - 支持横屏和竖屏模式
  - 适配不同屏幕尺寸和分辨率
  - 考虑安全区域（如刘海屏）
- **性能考虑**:
  - 优化移动设备的电池消耗
  - 减少不必要的重绘和重排
  - 适配低性能设备

### 文本游戏特有的前端需求

#### 文本展示与阅读体验
- **文本渲染优化**:
  - 支持富文本格式（粗体、斜体、颜色）
  - 实现文本打字机效果，增强沉浸感
  - 支持文本高亮和标记功能
- **阅读辅助**:
  - 提供阅读进度指示
  - 支持文本搜索和定位
  - 实现书签功能，标记重要情节
- **个性化设置**:
  - 可调节的字体大小和行间距
  - 多种主题色彩方案（日间/夜间模式）
  - 自定义界面布局选项

#### 叙事节奏控制
- **内容展示节奏**:
  - 支持分段显示长文本内容
  - 实现"继续阅读"按钮控制节奏
  - 提供快速浏览和详细阅读模式切换
- **历史记录管理**:
  - 完整的游戏历史记录查看
  - 支持历史记录的搜索和过滤
  - 实现历史记录的导出功能

#### 交互反馈增强
- **输入辅助**:
  - 智能命令提示和自动补全
  - 常用指令的快捷键支持
  - 输入历史记录和重复使用
- **视觉反馈**:
  - 动画效果增强交互体验
  - 状态变化的视觉提示
  - 重要事件的特殊效果展示

---

## 非功能性需求

### 性能需求
- **响应时间**:
  - 用户界面响应时间 < 100ms
  - AI推演响应时间 < 5秒（90%的情况下）
  - 数据库查询响应时间 < 200ms
- **并发处理**:
  - 支持至少1000个并发用户
  - 支持至少100个并发世界同时运行
- **资源使用**:
  - 内存使用优化，避免内存泄漏
  - CPU使用率在高峰期不超过80%

### 可扩展性需求
- **水平扩展**: 系统架构应支持通过增加服务器节点来扩展处理能力。
- **模块化设计**: 各个系统模块应低耦合，便于独立开发和部署。
- **插件机制**: 支持通过插件方式添加新的游戏机制和功能。
- **API版本管理**: 提供API版本控制，确保向后兼容性。

### 可靠性需求
- **数据持久化**: 所有游戏状态必须持久化存储在数据库中，并定期备份。
- **故障恢复**: 系统应能从各种故障中自动恢复，包括服务器崩溃、网络中断等。
- **数据一致性**: 确保游戏世界状态的一致性，避免数据冲突和丢失。
- **监控告警**: 实现全面的系统监控和自动告警机制。

### 安全需求
- **输入验证**: 对所有用户输入进行严格验证，防止注入攻击。
- **身份认证**: 实现安全的用户身份认证和会话管理。
- **数据加密**: 敏感数据在传输和存储时进行加密。
- **访问控制**: 实现基于角色的访问控制，保护管理功能。
- **内容安全**: 对AI生成的内容进行安全检查，过滤不当内容。

### 可维护性需求
- **代码质量**: 遵循编码规范，保持代码的可读性和可维护性。
- **文档完整**: 提供完整的技术文档和用户文档。
- **测试覆盖**: 实现全面的单元测试、集成测试和端到端测试。
- **日志记录**: 实现详细的日志记录，便于问题诊断和性能分析。

---

## 后台与运营 (Backend & Operations) - P2

### 管理后台 (Admin Panel)
管理后台是监控、调试和维护游戏世界健康度的核心工具。它不仅服务于运营，更是开发过程中验证 AI 逻辑、优化游戏体验的关键。

- **需求 1: 世界状态浏览器 (World State Explorer)**
  - **功能**: 提供一个界面，可以实时或按时间点回溯查看任何一个游戏世界（World Instance）的完整状态。
  - **数据**: 包括所有场景的描述、地图连接、所有角色（玩家和 NPC）的详细属性、物品、当前激活的事件和任务。
  - **用途**:
    - **开发/调试**: 精准复现玩家报告的 bug。开发者可以直接加载出问题的世界状态快照，而无需从头开始游戏。
    - **运营**: 查看世界是否在正常演化，或是否存在卡死、数据异常等问题。

- **需求 2: AI 交互日志 (AI Interaction Log)**
  - **功能**: 记录每一次向大型语言模型发起的请求（Prompt）和接收到的响应（Response）。
  - **数据**: 日志应包含时间戳、玩家 ID、世界 ID、完整的 Prompt 文本、完整的 Response 文本、响应延迟、Token 消耗量。
  - **用途**:
    - **开发/AI 逻辑验证**: 这是判断 AI 逻辑问题的核心工具。通过分析 Prompt 和 Response，可以评估：
      - **一致性 (Consistency)**: AI 的生成内容是否与之前的世界状态、角色设定相矛盾（例如，一个声称失忆的 NPC 突然回忆起所有事）。
      - **连贯性 (Coherence)**: 生成的事件和描述是否符合玩家输入的世界观。
      - **因果关系 (Causality)**: 玩家的行动是否产生了逻辑上合理的后果。不合理的后果（如“我喝了一口水，然后城市爆炸了”）可以被快速定位。
    - **运营**: 监控 AI 服务成本（Token 消耗），识别导致异常高消耗的交互模式。监控 API 响应时间，定位性能瓶颈。

- **需求 3: 玩家会话追溯 (Player Session Replay)**
  - **功能**: 能够按时间顺序，重放某个玩家在一个世界中的所有行动以及世界对这些行动的演化结果。
  - **数据**: 玩家输入的指令序列、每个指令执行后的时间点、当时的世界状态快照或演化日志。
  - **用途**:
    - **开发/用户体验分析**: 理解玩家行为模式，发现玩家普遍感到困惑或卡关的地方。
    - **运营/客服**: 处理玩家申诉，核实游戏内发生的事件。

### 数据分析与监控 (Analytics & Monitoring)
通过对运营数据的聚合分析，持续优化游戏和 AI 的表现。

- **需求 1: 核心指标仪表盘 (Core Metrics Dashboard)**
  - **功能**: 以图表形式展示游戏的核心运营指标。
  - **数据**: DAU/MAU（日/月活跃用户）、平均游戏时长、玩家留存率、世界创建数量等。
  - **用途**: 宏观了解游戏健康度和玩家参与度。

- **需求 2: AI 质量监控 (AI Quality Monitoring)**
  - **功能**: 自动化或半自动化地监控 AI 生成内容的质量。
  - **数据**:
    - **内容安全**: 对 AI 生成的文本进行敏感词、不当内容（Toxicity）的自动检测和告警。
    - **世界停滞检测 (Stagnation Detection)**: 分析世界状态的变化率。如果一个世界在长时间内没有新的事件、角色状态没有显著变化，系统应发出告警，可能表示 AI 进入了重复或无意义的循环。
    - **演化多样性分析**: 统计一段时间内生成事件、NPC 行为的多样性。如果多样性过低，说明 AI 的创造力可能正在衰减。
  - **用途**:
    - **开发**: 及时发现并调整 Prompt Engineering 策略，防止 AI 逻辑出现退化。
    - **运营**: 确保社区内容健康，维护良好的游戏环境。

- **需求 3: 性能与成本监控 (Performance & Cost Monitoring)**
  - **功能**: 监控服务器性能和 AI API 调用成本。
  - **数据**: 服务器 CPU/内存使用率、API 平均响应时间、总 Token 消耗、单位玩家平均成本。
  - **用途**:
    - **运营**: 进行容量规划和成本控制，确保服务稳定性和盈利能力。
---

## AI文本生成接口使用用例

### 核心AI接口调用场景
- **世界观生成接口**: 根据玩家提供的世界设定，调用AI接口生成宏观的世界观（JSON结构），并在此基础上，为玩家生成其角色初始位置以及周边的少量关联场景，构成初始“玩家视野”。
- **场景生成接口**: 根据玩家的当前位置按需调用AI接口生成新的JSON结构的场景信息。
- **邮件处理接口**: 通过有限队列和延迟时间任务将当前队列中的邮件发送给AI文本生成接口并返回每个邮件的回信，例如在1分钟内，或者超过100封邮件则触发批量处理。
- **世界演化接口**: 将游戏时刻、世界在前一时刻的切片信息发送给AI接口并返回对应新时刻世界的状态。
- **事件结果生成接口**: 通过有限队列延迟时间任务将当前队列中的NPC包括玩家触发的事件和对应的行动发送给AI接口并返回每个行动对应的结果。

### 异步处理机制
- **请求队列管理**: 使用Go语言的goroutine和channel实现高效的异步请求队列
- **批量处理优化**: 将相似的AI请求进行批量处理，提高接口调用效率
- **超时和重试机制**: 实现请求超时处理和智能重试策略
- **负载均衡**: 支持多个AI接口端点的负载均衡调用
- **缓存策略**: 对相似请求的结果进行缓存，减少重复的AI接口调用

### 接口调用优化
- **Prompt模板管理**: 标准化的Prompt模板，确保AI接口调用的一致性
- **响应解析**: 严格的JSON响应解析和验证机制
- **错误处理**: 完善的错误处理和降级策略
- **性能监控**: 实时监控AI接口调用的性能指标
- **成本控制**: 基于Token使用量的成本控制和预警机制

---

## 总结与后续规划

### 需求优先级
- **P0 (核心功能)**: 基础游戏循环、场景系统、角色系统、AI交互
- **P1 (重要功能)**: 前端界面、用户管理、世界分享、性能优化
- **P2 (增强功能)**: 管理后台、数据分析、社区功能、高级特性

### 开发里程碑
- **阶段1**: 完成核心后端系统和基础AI集成
- **阶段2**: 实现前端界面和基本用户体验
- **阶段3**: 添加用户管理和世界分享功能
- **阶段4**: 完善管理后台和运营工具
- **阶段5**: 优化性能和扩展高级功能

### 技术风险与应对
- **AI响应延迟**: 通过缓存、批处理和预生成内容来缓解
- **内容一致性**: 建立完善的状态管理和验证机制
- **扩展性挑战**: 采用微服务架构和云原生技术
- **成本控制**: 实现智能的AI调用优化和资源管理

本需求文档将作为项目开发的权威指南，后续的设计文档、技术选型和实现方案都将基于此文档进行。