# 服务器配置
SERVER_PORT=8080
SERVER_HOST=0.0.0.0
SERVER_READ_TIMEOUT=30s
SERVER_WRITE_TIMEOUT=30s
SERVER_IDLE_TIMEOUT=120s
ENVIRONMENT=development

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=ai_text_game
DB_SSL_MODE=disable
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_MAX_LIFETIME=5m

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=2

# JWT认证配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRATION=24h

# OAuth配置 - Google
OAUTH_GOOGLE_CLIENT_ID=your_google_client_id
OAUTH_GOOGLE_CLIENT_SECRET=your_google_client_secret
OAUTH_GOOGLE_REDIRECT_URL=http://localhost:8080/auth/google/callback

# OAuth配置 - GitHub
OAUTH_GITHUB_CLIENT_ID=your_github_client_id
OAUTH_GITHUB_CLIENT_SECRET=your_github_client_secret
OAUTH_GITHUB_REDIRECT_URL=http://localhost:8080/auth/github/callback

# AI服务配置
AI_BASE_URL=https://wm.atjog.com
AI_TOKEN=your_ai_token
AI_TIMEOUT=30s
AI_MAX_RETRIES=3
AI_RETRY_DELAY=1s
AI_QUEUE_SIZE=1000
AI_BATCH_SIZE=10
AI_BATCH_TIMEOUT=5s
AI_MOCK_ENABLED=true

# 游戏配置
GAME_MAX_WORLDS_PER_USER=10
GAME_MAX_PLAYERS_PER_WORLD=10
GAME_DEFAULT_TIME_RATE=1.0
GAME_TICK_INTERVAL=30s
GAME_MAX_MEMORY_PER_CHAR=100
GAME_MAX_EXPERIENCE_PER_CHAR=1000
