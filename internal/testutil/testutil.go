package testutil

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"testing"
	"time"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/internal/validation"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

// TestDB 测试数据库配置
type TestDB struct {
	DB     *gorm.DB
	Config *config.DatabaseConfig
}

// SetupTestDB 设置测试数据库
func SetupTestDB(t *testing.T) *TestDB {
	// 从环境变量获取测试数据库配置
	testDBConfig := &config.DatabaseConfig{
		Host:     getEnvOrDefault("TEST_DB_HOST", "localhost"),
		Port:     5432,
		User:     getEnvOrDefault("TEST_DB_USER", "postgres"),
		Password: getEnvOrDefault("TEST_DB_PASSWORD", ""),
		DBName:   getEnvOrDefault("TEST_DB_NAME", "ai_text_game_test"),
		SSLMode:  "disable",
	}

	// 创建测试数据库（如果不存在）
	createTestDatabase(t, testDBConfig)

	// 连接测试数据库
	db, err := gorm.Open(postgres.Open(testDBConfig.DSN()), &gorm.Config{
		Logger: gormLogger.Default.LogMode(gormLogger.Silent),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	require.NoError(t, err, "连接测试数据库失败")

	// 自动迁移表结构
	err = db.AutoMigrate(
		&models.User{},
		&models.UserStats{},
		&models.World{},
		&models.Scene{},
		&models.Character{},
		&models.Entity{},
		&models.Event{},
		&models.AIInteraction{},
		&validation.ValidationLog{},
	)
	require.NoError(t, err, "数据库迁移失败")

	return &TestDB{
		DB:     db,
		Config: testDBConfig,
	}
}

// createTestDatabase 创建测试数据库
func createTestDatabase(t *testing.T, config *config.DatabaseConfig) {
	// 连接到postgres数据库来创建测试数据库
	adminDSN := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=postgres sslmode=%s",
		config.Host, config.Port, config.User, config.Password, config.SSLMode)

	db, err := sql.Open("postgres", adminDSN)
	if err != nil {
		t.Skipf("无法连接到PostgreSQL服务器，跳过测试: %v", err)
		return
	}
	defer db.Close()

	// 检查测试数据库是否存在
	var exists bool
	err = db.QueryRow("SELECT EXISTS(SELECT datname FROM pg_catalog.pg_database WHERE datname = $1)", config.DBName).Scan(&exists)
	if err != nil {
		t.Skipf("检查数据库是否存在失败，跳过测试: %v", err)
		return
	}

	// 如果数据库不存在，创建它
	if !exists {
		_, err = db.Exec(fmt.Sprintf("CREATE DATABASE %s", config.DBName))
		if err != nil {
			t.Skipf("创建测试数据库失败，跳过测试: %v", err)
			return
		}
	}
}

// CleanupTestDB 清理测试数据库
func (tdb *TestDB) CleanupTestDB(t *testing.T) {
	// 清理所有表的数据
	tables := []string{
		"validation_logs",
		"ai_interactions",
		"events",
		"entities",
		"characters",
		"scenes",
		"worlds",
		"user_stats",
		"users",
	}

	for _, table := range tables {
		err := tdb.DB.Exec(fmt.Sprintf("TRUNCATE TABLE %s CASCADE", table)).Error
		if err != nil {
			t.Logf("清理表 %s 失败: %v", table, err)
		}
	}
}

// CreateTestUser 创建测试用户
func (tdb *TestDB) CreateTestUser(t *testing.T) *models.User {
	user := &models.User{
		ExternalID:       uuid.New().String(),
		ExternalProvider: "test",
		Email:            "<EMAIL>",
		DisplayName:      stringPtr("Test User"),
		GameRoles:        models.StringArray{"user"},
		Status:           "active",
		Preferences:      models.JSON{},
		IDPClaims:        models.JSON{},
	}

	err := tdb.DB.Create(user).Error
	require.NoError(t, err, "创建测试用户失败")

	return user
}

// CreateTestWorld 创建测试世界
func (tdb *TestDB) CreateTestWorld(t *testing.T, creatorID uuid.UUID) *models.World {
	world := &models.World{
		CreatorID:   creatorID,
		Name:        "Test World",
		Description: stringPtr("A test world for testing"),
		IsPublic:    true,
		MaxPlayers:  10,
		Status:      "active",
		Config:      models.JSON{},
		GlobalVars:  models.JSON{},
	}

	err := tdb.DB.Create(world).Error
	require.NoError(t, err, "创建测试世界失败")

	return world
}

// CreateTestScene 创建测试场景
func (tdb *TestDB) CreateTestScene(t *testing.T, worldID uuid.UUID) *models.Scene {
	scene := &models.Scene{
		WorldID:     worldID,
		Name:        "Test Scene",
		Description: stringPtr("A test scene for testing"),
		SceneType:   "normal",
		Properties:  models.JSON{},
		Environment: models.JSON{},
		Status:      "active",
	}

	err := tdb.DB.Create(scene).Error
	require.NoError(t, err, "创建测试场景失败")

	return scene
}

// CreateTestCharacter 创建测试角色
func (tdb *TestDB) CreateTestCharacter(t *testing.T, worldID, userID uuid.UUID) *models.Character {
	character := &models.Character{
		WorldID:       worldID,
		UserID:        &userID,
		Name:          "Test Character",
		Description:   stringPtr("A test character for testing"),
		CharacterType: "player",
		Traits:        models.StringArray{"brave", "kind"},
		Stats:         models.JSON{},
		Memories:      models.JSON{},
		Relationships: models.JSON{},
		Status:        "active",
	}

	err := tdb.DB.Create(character).Error
	require.NoError(t, err, "创建测试角色失败")

	return character
}

// CreateTestLogger 创建测试日志器
func CreateTestLogger() *logger.Logger {
	return logger.New("error") // 测试时使用error级别减少日志输出
}

// GetTestConfig 获取测试配置
func GetTestConfig() *config.Config {
	return &config.Config{
		Server: config.ServerConfig{
			Port:         "8080",
			Host:         "localhost",
			ReadTimeout:  30 * time.Second,
			WriteTimeout: 30 * time.Second,
			IdleTimeout:  60 * time.Second,
			Mode:         "test",
		},
		Database: config.DatabaseConfig{
			Host:         getEnvOrDefault("TEST_DB_HOST", "localhost"),
			Port:         5432,
			User:         getEnvOrDefault("TEST_DB_USER", "postgres"),
			Password:     getEnvOrDefault("TEST_DB_PASSWORD", ""),
			DBName:       getEnvOrDefault("TEST_DB_NAME", "ai_text_game_test"),
			SSLMode:      "disable",
			MaxOpenConns: 5,
			MaxIdleConns: 2,
			MaxLifetime:  time.Minute,
		},
		Auth: config.AuthConfig{
			JWT: config.JWTConfig{
				SecretKey:      "test-secret-key-for-testing-only",
				ExpirationTime: 24 * time.Hour,
				Issuer:         "ai-text-game-test",
			},
		},
		AI: config.AIConfig{
			Provider: "mock",
			MockMode: true,
		},
	}
}

// WithTestTransaction 在事务中执行测试
func (tdb *TestDB) WithTestTransaction(t *testing.T, fn func(*gorm.DB)) {
	tx := tdb.DB.Begin()
	require.NoError(t, tx.Error, "开始事务失败")

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
		tx.Rollback() // 测试结束后总是回滚
	}()

	fn(tx)
}

// AssertNoError 断言没有错误
func AssertNoError(t *testing.T, err error, msgAndArgs ...interface{}) {
	require.NoError(t, err, msgAndArgs...)
}

// AssertError 断言有错误
func AssertError(t *testing.T, err error, msgAndArgs ...interface{}) {
	require.Error(t, err, msgAndArgs...)
}

// AssertEqual 断言相等
func AssertEqual(t *testing.T, expected, actual interface{}, msgAndArgs ...interface{}) {
	require.Equal(t, expected, actual, msgAndArgs...)
}

// AssertNotEqual 断言不相等
func AssertNotEqual(t *testing.T, expected, actual interface{}, msgAndArgs ...interface{}) {
	require.NotEqual(t, expected, actual, msgAndArgs...)
}

// AssertTrue 断言为真
func AssertTrue(t *testing.T, value bool, msgAndArgs ...interface{}) {
	require.True(t, value, msgAndArgs...)
}

// AssertFalse 断言为假
func AssertFalse(t *testing.T, value bool, msgAndArgs ...interface{}) {
	require.False(t, value, msgAndArgs...)
}

// AssertNotNil 断言不为nil
func AssertNotNil(t *testing.T, object interface{}, msgAndArgs ...interface{}) {
	require.NotNil(t, object, msgAndArgs...)
}

// AssertNil 断言为nil
func AssertNil(t *testing.T, object interface{}, msgAndArgs ...interface{}) {
	require.Nil(t, object, msgAndArgs...)
}

// 辅助函数

// getEnvOrDefault 获取环境变量或默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// stringPtr 返回字符串指针
func stringPtr(s string) *string {
	return &s
}

// StringPtr 返回字符串指针（导出版本）
func StringPtr(s string) *string {
	return &s
}

// uuidPtr 返回UUID指针
func uuidPtr(id uuid.UUID) *uuid.UUID {
	return &id
}

// UUIDPtr 返回UUID指针（导出版本）
func UUIDPtr(id uuid.UUID) *uuid.UUID {
	return &id
}

// BoolPtr 返回布尔值指针
func BoolPtr(b bool) *bool {
	return &b
}

// IntPtr 返回整数指针
func IntPtr(i int) *int {
	return &i
}

// Int64Ptr 返回int64指针
func Int64Ptr(i int64) *int64 {
	return &i
}

// SkipIfNoDatabase 如果没有数据库连接则跳过测试
func SkipIfNoDatabase(t *testing.T) {
	if os.Getenv("SKIP_DB_TESTS") == "true" {
		t.Skip("跳过数据库测试")
	}
}

// SetupTestContext 设置测试上下文
func SetupTestContext() context.Context {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	// 注意：在实际测试中，你需要在适当的时候调用cancel()
	_ = cancel
	return ctx
}

// LogTestInfo 记录测试信息
func LogTestInfo(t *testing.T, message string, args ...interface{}) {
	if testing.Verbose() {
		log.Printf("[TEST] "+message, args...)
	}
}
