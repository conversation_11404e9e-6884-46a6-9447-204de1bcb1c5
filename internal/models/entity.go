package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Entity 实体模型
type Entity struct {
	ID             uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	WorldID        uuid.UUID      `json:"world_id" gorm:"type:uuid;not null;index"`
	Name           string         `json:"name" gorm:"not null;size:200"`
	Description    *string        `json:"description"`
	EntityType     string         `json:"entity_type" gorm:"not null;index"` // item, event, goal, weather, etc.
	Properties     JSON           `json:"properties" gorm:"type:jsonb;default:'{}'"`
	Traits         StringArray    `json:"traits" gorm:"type:jsonb;default:'[]'"`
	CurrentSceneID *uuid.UUID     `json:"current_scene_id" gorm:"type:uuid;index"`
	OwnerID        *uuid.UUID     `json:"owner_id" gorm:"type:uuid;index"`      // 拥有者角色ID
	Status         string         `json:"status" gorm:"default:'active';index"` // active, inactive, consumed
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// 关联
	World        World      `json:"world,omitempty" gorm:"foreignKey:WorldID"`
	CurrentScene *Scene     `json:"current_scene,omitempty" gorm:"foreignKey:CurrentSceneID"`
	Owner        *Character `json:"owner,omitempty" gorm:"foreignKey:OwnerID"`
}

// Event 事件模型
type Event struct {
	ID           uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	WorldID      uuid.UUID      `json:"world_id" gorm:"type:uuid;not null;index"`
	SceneID      *uuid.UUID     `json:"scene_id" gorm:"type:uuid;index"`
	CreatorID    *uuid.UUID     `json:"creator_id" gorm:"type:uuid;index"` // 创建事件的角色ID
	EventType    string         `json:"event_type" gorm:"not null;index"`  // action, environment, system, etc.
	Name         *string        `json:"name" gorm:"size:200"`
	Description  *string        `json:"description"`
	EventData    JSON           `json:"event_data" gorm:"type:jsonb;default:'{}'"`
	Participants UUIDArray      `json:"participants" gorm:"type:jsonb;default:'[]'"` // 参与者ID列表
	Status       string         `json:"status" gorm:"default:'pending';index"`       // pending, processing, completed, failed
	Priority     int            `json:"priority" gorm:"default:0;index"`
	ScheduledAt  *time.Time     `json:"scheduled_at" gorm:"index"`
	ProcessedAt  *time.Time     `json:"processed_at"`
	Result       JSON           `json:"result" gorm:"type:jsonb"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// 关联
	World   World      `json:"world,omitempty" gorm:"foreignKey:WorldID"`
	Scene   *Scene     `json:"scene,omitempty" gorm:"foreignKey:SceneID"`
	Creator *Character `json:"creator,omitempty" gorm:"foreignKey:CreatorID"`
}

// AIInteraction AI交互日志模型
type AIInteraction struct {
	ID              uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	WorldID         *uuid.UUID `json:"world_id" gorm:"type:uuid;index"`
	UserID          *uuid.UUID `json:"user_id" gorm:"type:uuid;index"`
	InteractionType string     `json:"interaction_type" gorm:"not null;index"`
	Prompt          string     `json:"prompt" gorm:"not null"`
	Response        *string    `json:"response"`
	ResponseSchema  JSON       `json:"response_schema" gorm:"type:jsonb"`
	TokenUsage      *int       `json:"token_usage"`
	ResponseTime    *int       `json:"response_time"` // 响应时间(毫秒)
	Status          string     `json:"status" gorm:"default:'pending';index"`
	ErrorMessage    *string    `json:"error_message"`
	CreatedAt       time.Time  `json:"created_at" gorm:"index"`

	// 关联
	World *World `json:"world,omitempty" gorm:"foreignKey:WorldID"`
	User  *User  `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (Entity) TableName() string {
	return "entities"
}

// TableName 指定表名
func (Event) TableName() string {
	return "events"
}

// TableName 指定表名
func (AIInteraction) TableName() string {
	return "ai_interactions"
}

// BeforeCreate GORM钩子 - 创建前
func (e *Entity) BeforeCreate(tx *gorm.DB) error {
	if e.ID == uuid.Nil {
		e.ID = uuid.New()
	}
	return nil
}

// BeforeCreate GORM钩子 - 创建前
func (e *Event) BeforeCreate(tx *gorm.DB) error {
	if e.ID == uuid.Nil {
		e.ID = uuid.New()
	}
	return nil
}

// BeforeCreate GORM钩子 - 创建前
func (ai *AIInteraction) BeforeCreate(tx *gorm.DB) error {
	if ai.ID == uuid.Nil {
		ai.ID = uuid.New()
	}
	return nil
}

// Entity 方法

// IsActive 检查实体是否活跃
func (e *Entity) IsActive() bool {
	return e.Status == "active"
}

// IsConsumed 检查实体是否已被消耗
func (e *Entity) IsConsumed() bool {
	return e.Status == "consumed"
}

// IsItem 检查是否为物品
func (e *Entity) IsItem() bool {
	return e.EntityType == "item"
}

// IsGoal 检查是否为目标
func (e *Entity) IsGoal() bool {
	return e.EntityType == "goal"
}

// MoveTo 移动实体到指定场景
func (e *Entity) MoveTo(tx *gorm.DB, sceneID uuid.UUID) error {
	// 从当前场景移除
	if e.CurrentSceneID != nil {
		var currentScene Scene
		if err := tx.First(&currentScene, *e.CurrentSceneID).Error; err == nil {
			currentScene.RemoveEntity(tx, e.ID)
		}
	}

	// 移动到新场景
	e.CurrentSceneID = &sceneID
	if err := tx.Model(e).Update("current_scene_id", sceneID).Error; err != nil {
		return err
	}

	// 添加到新场景
	var newScene Scene
	if err := tx.First(&newScene, sceneID).Error; err != nil {
		return err
	}
	return newScene.AddEntity(tx, e.ID)
}

// SetOwner 设置拥有者
func (e *Entity) SetOwner(tx *gorm.DB, ownerID uuid.UUID) error {
	e.OwnerID = &ownerID
	return tx.Model(e).Update("owner_id", ownerID).Error
}

// RemoveOwner 移除拥有者
func (e *Entity) RemoveOwner(tx *gorm.DB) error {
	e.OwnerID = nil
	return tx.Model(e).Update("owner_id", nil).Error
}

// AddTrait 添加特质
func (e *Entity) AddTrait(tx *gorm.DB, trait string) error {
	// 检查是否已有该特质
	for _, t := range e.Traits {
		if t == trait {
			return nil
		}
	}

	e.Traits = append(e.Traits, trait)
	return tx.Model(e).Update("traits", e.Traits).Error
}

// RemoveTrait 移除特质
func (e *Entity) RemoveTrait(tx *gorm.DB, trait string) error {
	filtered := make(StringArray, 0, len(e.Traits))
	for _, t := range e.Traits {
		if t != trait {
			filtered = append(filtered, t)
		}
	}

	e.Traits = filtered
	return tx.Model(e).Update("traits", e.Traits).Error
}

// HasTrait 检查是否有指定特质
func (e *Entity) HasTrait(trait string) bool {
	for _, t := range e.Traits {
		if t == trait {
			return true
		}
	}
	return false
}

// SetProperty 设置属性
func (e *Entity) SetProperty(tx *gorm.DB, key string, value interface{}) error {
	e.Properties[key] = value
	return tx.Model(e).Update("properties", e.Properties).Error
}

// GetProperty 获取属性
func (e *Entity) GetProperty(key string) interface{} {
	return e.Properties[key]
}

// Consume 消耗实体
func (e *Entity) Consume(tx *gorm.DB) error {
	e.Status = "consumed"
	return tx.Model(e).Update("status", "consumed").Error
}

// Event 方法

// IsPending 检查事件是否待处理
func (e *Event) IsPending() bool {
	return e.Status == "pending"
}

// IsProcessing 检查事件是否正在处理
func (e *Event) IsProcessing() bool {
	return e.Status == "processing"
}

// IsCompleted 检查事件是否已完成
func (e *Event) IsCompleted() bool {
	return e.Status == "completed"
}

// IsFailed 检查事件是否失败
func (e *Event) IsFailed() bool {
	return e.Status == "failed"
}

// AddParticipant 添加参与者
func (e *Event) AddParticipant(tx *gorm.DB, participantID uuid.UUID) error {
	// 检查是否已存在
	for _, id := range e.Participants {
		if id == participantID {
			return nil
		}
	}

	e.Participants = append(e.Participants, participantID)
	return tx.Model(e).Update("participants", e.Participants).Error
}

// RemoveParticipant 移除参与者
func (e *Event) RemoveParticipant(tx *gorm.DB, participantID uuid.UUID) error {
	filtered := make(UUIDArray, 0, len(e.Participants))
	for _, id := range e.Participants {
		if id != participantID {
			filtered = append(filtered, id)
		}
	}

	e.Participants = filtered
	return tx.Model(e).Update("participants", e.Participants).Error
}

// HasParticipant 检查是否有指定参与者
func (e *Event) HasParticipant(participantID uuid.UUID) bool {
	for _, id := range e.Participants {
		if id == participantID {
			return true
		}
	}
	return false
}

// SetStatus 设置事件状态
func (e *Event) SetStatus(tx *gorm.DB, status string) error {
	e.Status = status
	if status == "processing" {
		now := time.Now()
		e.ProcessedAt = &now
		return tx.Model(e).Updates(map[string]interface{}{
			"status":       status,
			"processed_at": now,
		}).Error
	}
	return tx.Model(e).Update("status", status).Error
}

// SetResult 设置事件结果
func (e *Event) SetResult(tx *gorm.DB, result map[string]interface{}) error {
	e.Result = JSON(result)
	e.Status = "completed"
	now := time.Now()
	e.ProcessedAt = &now

	return tx.Model(e).Updates(map[string]interface{}{
		"result":       e.Result,
		"status":       "completed",
		"processed_at": now,
	}).Error
}

// SetError 设置事件错误
func (e *Event) SetError(tx *gorm.DB, errorMsg string) error {
	e.Status = "failed"
	now := time.Now()
	e.ProcessedAt = &now

	return tx.Model(e).Updates(map[string]interface{}{
		"status":       "failed",
		"processed_at": now,
	}).Error
}

// IsScheduled 检查事件是否已安排时间
func (e *Event) IsScheduled() bool {
	return e.ScheduledAt != nil
}

// IsReady 检查事件是否准备执行
func (e *Event) IsReady() bool {
	if !e.IsScheduled() {
		return true // 没有安排时间的事件立即准备
	}
	return time.Now().After(*e.ScheduledAt)
}

// AIInteraction 方法

// IsCompleted 检查AI交互是否完成
func (ai *AIInteraction) IsCompleted() bool {
	return ai.Status == "completed"
}

// IsFailed 检查AI交互是否失败
func (ai *AIInteraction) IsFailed() bool {
	return ai.Status == "failed"
}

// SetCompleted 设置为完成状态
func (ai *AIInteraction) SetCompleted(tx *gorm.DB, response string, tokenUsage, responseTime int) error {
	ai.Status = "completed"
	ai.Response = &response
	ai.TokenUsage = &tokenUsage
	ai.ResponseTime = &responseTime

	return tx.Model(ai).Updates(map[string]interface{}{
		"status":        "completed",
		"response":      response,
		"token_usage":   tokenUsage,
		"response_time": responseTime,
	}).Error
}

// SetFailed 设置为失败状态
func (ai *AIInteraction) SetFailed(tx *gorm.DB, errorMsg string) error {
	ai.Status = "failed"
	ai.ErrorMessage = &errorMsg

	return tx.Model(ai).Updates(map[string]interface{}{
		"status":        "failed",
		"error_message": errorMsg,
	}).Error
}
