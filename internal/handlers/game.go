package handlers

import (
	"net/http"
	"strconv"

	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/internal/game"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// GameHandler 游戏处理器
type GameHandler struct {
	worldService     *game.WorldService
	characterService *game.CharacterService
	sceneService     *game.SceneService
	eventService     *game.EventService
}

// NewGameHandler 创建游戏处理器
func NewGameHandler(
	worldService *game.WorldService,
	characterService *game.CharacterService,
	sceneService *game.SceneService,
	eventService *game.EventService,
) *GameHandler {
	return &GameHandler{
		worldService:     worldService,
		characterService: characterService,
		sceneService:     sceneService,
		eventService:     eventService,
	}
}

// CreateWorld 创建世界
// @Summary 创建新世界
// @Description 创建一个新的游戏世界
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateWorldRequest true "创建世界请求"
// @Success 200 {object} Response{data=models.World}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds [post]
func (h *GameHandler) CreateWorld(c *gin.Context) {
	var req CreateWorldRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 设置默认配置
	if req.Config == nil {
		req.Config = make(map[string]interface{})
	}

	world, err := h.worldService.CreateWorld(c.Request.Context(), userID, req.Name, req.Description, req.Config)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建世界失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "创建世界成功",
		Data:    world,
	})
}

// GetWorld 获取世界信息
// @Summary 获取世界信息
// @Description 获取指定世界的详细信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response{data=models.World}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /game/worlds/{world_id} [get]
func (h *GameHandler) GetWorld(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	world, err := h.worldService.GetWorld(c.Request.Context(), worldID)
	if err != nil {
		if err.Error() == "世界不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "世界不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取世界信息成功",
		Data:    world,
	})
}

// GetMyWorlds 获取我的世界列表
// @Summary 获取我的世界列表
// @Description 获取当前用户创建的世界列表
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} Response{data=PaginatedResponse}
// @Failure 401 {object} Response
// @Router /game/my-worlds [get]
func (h *GameHandler) GetMyWorlds(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	worlds, total, err := h.worldService.GetWorldsByCreator(c.Request.Context(), userID, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取世界列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取世界列表成功",
		Data: PaginatedResponse{
			Items:      worlds,
			Total:      total,
			Page:       page,
			Limit:      limit,
			TotalPages: (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// GetPublicWorlds 获取公开世界列表
// @Summary 获取公开世界列表
// @Description 获取所有公开的世界列表
// @Tags Game
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} Response{data=PaginatedResponse}
// @Router /game/public-worlds [get]
func (h *GameHandler) GetPublicWorlds(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	worlds, total, err := h.worldService.GetPublicWorlds(c.Request.Context(), limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取公开世界列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取公开世界列表成功",
		Data: PaginatedResponse{
			Items:      worlds,
			Total:      total,
			Page:       page,
			Limit:      limit,
			TotalPages: (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// JoinWorld 加入世界
// @Summary 加入世界
// @Description 用户加入指定的世界
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/join [post]
func (h *GameHandler) JoinWorld(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.worldService.JoinWorld(c.Request.Context(), worldID, userID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "加入世界失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "成功加入世界",
	})
}

// LeaveWorld 离开世界
// @Summary 离开世界
// @Description 用户离开指定的世界
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param world_id path string true "世界ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/worlds/{world_id}/leave [post]
func (h *GameHandler) LeaveWorld(c *gin.Context) {
	worldIDStr := c.Param("world_id")
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的世界ID格式",
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.worldService.LeaveWorld(c.Request.Context(), worldID, userID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "离开世界失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "成功离开世界",
	})
}

// CreateCharacter 创建角色
// @Summary 创建角色
// @Description 在指定世界中创建新角色
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateCharacterRequest true "创建角色请求"
// @Success 200 {object} Response{data=models.Character}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters [post]
func (h *GameHandler) CreateCharacter(c *gin.Context) {
	var req CreateCharacterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	character, err := h.characterService.CreateCharacter(
		c.Request.Context(),
		req.WorldID,
		&userID,
		req.Name,
		req.Description,
		req.CharacterType,
		req.Traits,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建角色失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "创建角色成功",
		Data:    character,
	})
}

// GetCharacter 获取角色信息
// @Summary 获取角色信息
// @Description 获取指定角色的详细信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Success 200 {object} Response{data=models.Character}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /game/characters/{character_id} [get]
func (h *GameHandler) GetCharacter(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	character, err := h.characterService.GetCharacter(c.Request.Context(), characterID)
	if err != nil {
		if err.Error() == "角色不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "角色不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取角色信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取角色信息成功",
		Data:    character,
	})
}

// GetMyCharacters 获取我的角色列表
// @Summary 获取我的角色列表
// @Description 获取当前用户的角色列表
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} Response{data=PaginatedResponse}
// @Failure 401 {object} Response
// @Router /game/my-characters [get]
func (h *GameHandler) GetMyCharacters(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}
	offset := (page - 1) * limit

	characters, total, err := h.characterService.GetCharactersByUser(c.Request.Context(), userID, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取角色列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取角色列表成功",
		Data: PaginatedResponse{
			Items:      characters,
			Total:      total,
			Page:       page,
			Limit:      limit,
			TotalPages: (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// 请求结构体定义

// CreateWorldRequest 创建世界请求
type CreateWorldRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	Config      map[string]interface{} `json:"config"`
}

// CreateCharacterRequest 创建角色请求
type CreateCharacterRequest struct {
	WorldID       uuid.UUID `json:"world_id" binding:"required"`
	Name          string    `json:"name" binding:"required"`
	Description   string    `json:"description"`
	CharacterType string    `json:"character_type" binding:"required"`
	Traits        []string  `json:"traits"`
}

// MoveCharacter 移动角色
// @Summary 移动角色
// @Description 移动角色到指定场景
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param character_id path string true "角色ID"
// @Param request body MoveCharacterRequest true "移动角色请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/characters/{character_id}/move [post]
func (h *GameHandler) MoveCharacter(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的角色ID格式",
		})
		return
	}

	var req MoveCharacterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := auth.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, Response{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	if err := h.characterService.MoveCharacter(c.Request.Context(), characterID, req.SceneID, &userID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "移动角色失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "角色移动成功",
	})
}

// CreateScene 创建场景
// @Summary 创建场景
// @Description 在指定世界中创建新场景
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateSceneRequest true "创建场景请求"
// @Success 200 {object} Response{data=models.Scene}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/scenes [post]
func (h *GameHandler) CreateScene(c *gin.Context) {
	var req CreateSceneRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 设置默认属性
	if req.Properties == nil {
		req.Properties = make(map[string]interface{})
	}

	scene, err := h.sceneService.CreateScene(
		c.Request.Context(),
		req.WorldID,
		req.Name,
		req.Description,
		req.SceneType,
		req.Properties,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建场景失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "创建场景成功",
		Data:    scene,
	})
}

// GetScene 获取场景信息
// @Summary 获取场景信息
// @Description 获取指定场景的详细信息
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param scene_id path string true "场景ID"
// @Success 200 {object} Response{data=models.Scene}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Router /game/scenes/{scene_id} [get]
func (h *GameHandler) GetScene(c *gin.Context) {
	sceneIDStr := c.Param("scene_id")
	sceneID, err := uuid.Parse(sceneIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的场景ID格式",
		})
		return
	}

	scene, err := h.sceneService.GetScene(c.Request.Context(), sceneID)
	if err != nil {
		if err.Error() == "场景不存在" {
			c.JSON(http.StatusNotFound, Response{
				Success: false,
				Message: "场景不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "获取场景信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取场景信息成功",
		Data:    scene,
	})
}

// CreateEvent 创建事件
// @Summary 创建事件
// @Description 在指定世界中创建新事件
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateEventRequest true "创建事件请求"
// @Success 200 {object} Response{data=models.Event}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/events [post]
func (h *GameHandler) CreateEvent(c *gin.Context) {
	var req CreateEventRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Priority == 0 {
		req.Priority = 5
	}
	if req.EventData == nil {
		req.EventData = make(map[string]interface{})
	}

	event, err := h.eventService.CreateEvent(
		c.Request.Context(),
		req.WorldID,
		req.EventType,
		req.Name,
		req.Description,
		req.Priority,
		req.Participants,
		req.EventData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "创建事件失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "创建事件成功",
		Data:    event,
	})
}

// ProcessEvent 处理事件
// @Summary 处理事件
// @Description 处理指定的事件
// @Tags Game
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param event_id path string true "事件ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /game/events/{event_id}/process [post]
func (h *GameHandler) ProcessEvent(c *gin.Context) {
	eventIDStr := c.Param("event_id")
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, Response{
			Success: false,
			Message: "无效的事件ID格式",
		})
		return
	}

	if err := h.eventService.ProcessEvent(c.Request.Context(), eventID); err != nil {
		c.JSON(http.StatusInternalServerError, Response{
			Success: false,
			Message: "处理事件失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "事件处理成功",
	})
}

// 请求结构体定义

// MoveCharacterRequest 移动角色请求
type MoveCharacterRequest struct {
	SceneID uuid.UUID `json:"scene_id" binding:"required"`
}

// CreateSceneRequest 创建场景请求
type CreateSceneRequest struct {
	WorldID     uuid.UUID              `json:"world_id" binding:"required"`
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	SceneType   string                 `json:"scene_type" binding:"required"`
	Properties  map[string]interface{} `json:"properties"`
}

// CreateEventRequest 创建事件请求
type CreateEventRequest struct {
	WorldID      uuid.UUID              `json:"world_id" binding:"required"`
	EventType    string                 `json:"event_type" binding:"required"`
	Name         string                 `json:"name" binding:"required"`
	Description  string                 `json:"description"`
	Priority     int                    `json:"priority"`
	Participants []uuid.UUID            `json:"participants"`
	EventData    map[string]interface{} `json:"event_data"`
}

// PaginatedResponse 分页响应
type PaginatedResponse struct {
	Items      interface{} `json:"items"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	TotalPages int64       `json:"total_pages"`
}
