package game

import (
	"context"
	"fmt"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SceneService 场景管理服务
type SceneService struct {
	db     *gorm.DB
	logger *logger.Logger
}

// NewSceneService 创建场景管理服务
func NewSceneService(db *gorm.DB, logger *logger.Logger) *SceneService {
	return &SceneService{
		db:     db,
		logger: logger,
	}
}

// CreateScene 创建场景
func (s *SceneService) CreateScene(ctx context.Context, worldID uuid.UUID, name, description string, sceneType string, properties map[string]interface{}) (*models.Scene, error) {
	scene := &models.Scene{
		WorldID:         worldID,
		Name:            name,
		Description:     &description,
		SceneType:       sceneType,
		Properties:      models.JSON(properties),
		EntitiesPresent: models.UUIDArray{},
		ConnectedScenes: models.JSON{},
		Status:          "active",
	}

	if err := s.db.WithContext(ctx).Create(scene).Error; err != nil {
		s.logger.Error("创建场景失败", "error", err, "world_id", worldID, "name", name)
		return nil, fmt.Errorf("创建场景失败: %w", err)
	}

	s.logger.Info("成功创建场景", "scene_id", scene.ID, "world_id", worldID, "name", name, "type", sceneType)
	return scene, nil
}

// GetScene 获取场景信息
func (s *SceneService) GetScene(ctx context.Context, sceneID uuid.UUID) (*models.Scene, error) {
	var scene models.Scene
	err := s.db.WithContext(ctx).
		Preload("World").
		Preload("Characters").
		Preload("Entities").
		First(&scene, sceneID).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("场景不存在")
		}
		s.logger.Error("获取场景信息失败", "error", err, "scene_id", sceneID)
		return nil, fmt.Errorf("获取场景信息失败: %w", err)
	}

	return &scene, nil
}

// GetScenesByWorld 获取世界中的场景列表
func (s *SceneService) GetScenesByWorld(ctx context.Context, worldID uuid.UUID, sceneType string, limit, offset int) ([]models.Scene, int64, error) {
	var scenes []models.Scene
	var total int64

	query := s.db.WithContext(ctx).Where("world_id = ?", worldID)
	if sceneType != "" {
		query = query.Where("scene_type = ?", sceneType)
	}

	// 获取总数
	if err := query.Model(&models.Scene{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取场景总数失败: %w", err)
	}

	// 获取场景列表
	err := query.Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&scenes).Error

	if err != nil {
		s.logger.Error("获取世界场景列表失败", "error", err, "world_id", worldID)
		return nil, 0, fmt.Errorf("获取场景列表失败: %w", err)
	}

	return scenes, total, nil
}

// UpdateScene 更新场景信息
func (s *SceneService) UpdateScene(ctx context.Context, sceneID uuid.UUID, updates map[string]interface{}) error {
	result := s.db.WithContext(ctx).Model(&models.Scene{}).Where("id = ?", sceneID).Updates(updates)
	if result.Error != nil {
		s.logger.Error("更新场景信息失败", "error", result.Error, "scene_id", sceneID)
		return fmt.Errorf("更新场景信息失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("场景不存在")
	}

	s.logger.Info("成功更新场景信息", "scene_id", sceneID, "updates", updates)
	return nil
}

// DeleteScene 删除场景
func (s *SceneService) DeleteScene(ctx context.Context, sceneID uuid.UUID, worldID uuid.UUID) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 检查场景是否存在且属于指定世界
		var scene models.Scene
		if err := tx.Where("id = ? AND world_id = ?", sceneID, worldID).First(&scene).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("场景不存在或不属于该世界")
			}
			return fmt.Errorf("检查场景权限失败: %w", err)
		}

		// 检查是否有角色在该场景中
		var characterCount int64
		if err := tx.Model(&models.Character{}).Where("current_scene_id = ?", sceneID).Count(&characterCount).Error; err != nil {
			return fmt.Errorf("检查场景中角色数量失败: %w", err)
		}

		if characterCount > 0 {
			return fmt.Errorf("场景中还有角色，无法删除")
		}

		// 删除场景
		if err := tx.Delete(&scene).Error; err != nil {
			return fmt.Errorf("删除场景失败: %w", err)
		}

		s.logger.Info("成功删除场景", "scene_id", sceneID, "world_id", worldID)
		return nil
	})
}

// ConnectScenes 连接两个场景
func (s *SceneService) ConnectScenes(ctx context.Context, fromSceneID uuid.UUID, toSceneID uuid.UUID, direction string, bidirectional bool) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取源场景
		var fromScene models.Scene
		if err := tx.First(&fromScene, fromSceneID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("源场景不存在")
			}
			return fmt.Errorf("获取源场景失败: %w", err)
		}

		// 获取目标场景
		var toScene models.Scene
		if err := tx.First(&toScene, toSceneID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("目标场景不存在")
			}
			return fmt.Errorf("获取目标场景失败: %w", err)
		}

		// 检查是否属于同一世界
		if fromScene.WorldID != toScene.WorldID {
			return fmt.Errorf("场景不属于同一世界")
		}

		// 连接场景
		if err := fromScene.ConnectScene(tx, direction, toSceneID); err != nil {
			return fmt.Errorf("连接场景失败: %w", err)
		}

		// 如果是双向连接
		if bidirectional {
			reverseDirection := getReverseDirection(direction)
			if reverseDirection != "" {
				if err := toScene.ConnectScene(tx, reverseDirection, fromSceneID); err != nil {
					return fmt.Errorf("创建反向连接失败: %w", err)
				}
			}
		}

		s.logger.Info("成功连接场景", "from_scene", fromSceneID, "to_scene", toSceneID, "direction", direction, "bidirectional", bidirectional)
		return nil
	})
}

// DisconnectScenes 断开场景连接
func (s *SceneService) DisconnectScenes(ctx context.Context, fromSceneID uuid.UUID, direction string, bidirectional bool) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取源场景
		var fromScene models.Scene
		if err := tx.First(&fromScene, fromSceneID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("源场景不存在")
			}
			return fmt.Errorf("获取源场景失败: %w", err)
		}

		// 获取连接的目标场景ID
		toSceneID := fromScene.GetConnectedSceneID(direction)

		// 断开连接
		if err := fromScene.DisconnectScene(tx, direction); err != nil {
			return fmt.Errorf("断开场景连接失败: %w", err)
		}

		// 如果是双向断开且有目标场景
		if bidirectional && toSceneID != nil {
			var toScene models.Scene
			if err := tx.First(&toScene, *toSceneID).Error; err == nil {
				reverseDirection := getReverseDirection(direction)
				if reverseDirection != "" {
					toScene.DisconnectScene(tx, reverseDirection)
				}
			}
		}

		s.logger.Info("成功断开场景连接", "from_scene", fromSceneID, "direction", direction, "bidirectional", bidirectional)
		return nil
	})
}

// AddEntityToScene 添加实体到场景
func (s *SceneService) AddEntityToScene(ctx context.Context, sceneID uuid.UUID, entityID uuid.UUID) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var scene models.Scene
		if err := tx.First(&scene, sceneID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("场景不存在")
			}
			return fmt.Errorf("获取场景失败: %w", err)
		}

		// 检查实体是否存在
		var entity models.Entity
		if err := tx.First(&entity, entityID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("实体不存在")
			}
			return fmt.Errorf("获取实体失败: %w", err)
		}

		// 检查是否属于同一世界
		if scene.WorldID != entity.WorldID {
			return fmt.Errorf("场景和实体不属于同一世界")
		}

		if err := scene.AddEntity(tx, entityID); err != nil {
			return fmt.Errorf("添加实体到场景失败: %w", err)
		}

		s.logger.Info("成功添加实体到场景", "scene_id", sceneID, "entity_id", entityID)
		return nil
	})
}

// RemoveEntityFromScene 从场景移除实体
func (s *SceneService) RemoveEntityFromScene(ctx context.Context, sceneID uuid.UUID, entityID uuid.UUID) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var scene models.Scene
		if err := tx.First(&scene, sceneID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("场景不存在")
			}
			return fmt.Errorf("获取场景失败: %w", err)
		}

		if err := scene.RemoveEntity(tx, entityID); err != nil {
			return fmt.Errorf("从场景移除实体失败: %w", err)
		}

		s.logger.Info("成功从场景移除实体", "scene_id", sceneID, "entity_id", entityID)
		return nil
	})
}

// UpdateSceneEnvironment 更新场景环境
func (s *SceneService) UpdateSceneEnvironment(ctx context.Context, sceneID uuid.UUID, environment map[string]interface{}) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var scene models.Scene
		if err := tx.First(&scene, sceneID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("场景不存在")
			}
			return fmt.Errorf("获取场景失败: %w", err)
		}

		if err := scene.UpdateEnvironment(tx, environment); err != nil {
			return fmt.Errorf("更新场景环境失败: %w", err)
		}

		s.logger.Info("成功更新场景环境", "scene_id", sceneID, "environment", environment)
		return nil
	})
}

// SetSceneProperty 设置场景属性
func (s *SceneService) SetSceneProperty(ctx context.Context, sceneID uuid.UUID, key string, value interface{}) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var scene models.Scene
		if err := tx.First(&scene, sceneID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("场景不存在")
			}
			return fmt.Errorf("获取场景失败: %w", err)
		}

		if err := scene.SetProperty(tx, key, value); err != nil {
			return fmt.Errorf("设置场景属性失败: %w", err)
		}

		s.logger.Info("成功设置场景属性", "scene_id", sceneID, "key", key, "value", value)
		return nil
	})
}

// GetConnectedScenes 获取连接的场景
func (s *SceneService) GetConnectedScenes(ctx context.Context, sceneID uuid.UUID) (map[string]*models.Scene, error) {
	scene, err := s.GetScene(ctx, sceneID)
	if err != nil {
		return nil, err
	}

	connections := scene.GetConnections()
	connectedScenes := make(map[string]*models.Scene)

	for direction, sceneIDStr := range connections {
		if connectedSceneID, err := uuid.Parse(sceneIDStr); err == nil {
			if connectedScene, err := s.GetScene(ctx, connectedSceneID); err == nil {
				connectedScenes[direction] = connectedScene
			}
		}
	}

	return connectedScenes, nil
}

// GetSceneStats 获取场景统计信息
func (s *SceneService) GetSceneStats(ctx context.Context, sceneID uuid.UUID) (map[string]interface{}, error) {
	scene, err := s.GetScene(ctx, sceneID)
	if err != nil {
		return nil, err
	}

	// 获取场景中的角色数量
	var characterCount int64
	s.db.WithContext(ctx).Model(&models.Character{}).Where("current_scene_id = ?", sceneID).Count(&characterCount)

	// 获取场景中的实体数量
	entityCount := scene.GetEntityCount()

	// 获取连接数量
	connections := scene.GetConnections()
	connectionCount := len(connections)

	stats := map[string]interface{}{
		"scene_id":         scene.ID,
		"name":             scene.Name,
		"type":             scene.SceneType,
		"status":           scene.Status,
		"world_id":         scene.WorldID,
		"character_count":  characterCount,
		"entity_count":     entityCount,
		"connection_count": connectionCount,
		"connections":      connections,
		"weather":          scene.GetWeather(),
		"temperature":      scene.GetTemperature(),
		"lighting":         scene.GetLighting(),
		"atmosphere":       scene.GetAtmosphere(),
		"created_at":       scene.CreatedAt,
		"updated_at":       scene.UpdatedAt,
	}

	return stats, nil
}

// getReverseDirection 获取反向方向
func getReverseDirection(direction string) string {
	reverseMap := map[string]string{
		"north": "south",
		"south": "north",
		"east":  "west",
		"west":  "east",
		"up":    "down",
		"down":  "up",
		"in":    "out",
		"out":   "in",
	}

	return reverseMap[direction]
}

// SearchScenes 搜索场景
func (s *SceneService) SearchScenes(ctx context.Context, worldID uuid.UUID, keyword string, limit int) ([]models.Scene, error) {
	var scenes []models.Scene

	query := s.db.WithContext(ctx).Where("world_id = ?", worldID)
	if keyword != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	err := query.Order("name ASC").
		Limit(limit).
		Find(&scenes).Error

	if err != nil {
		s.logger.Error("搜索场景失败", "error", err, "world_id", worldID, "keyword", keyword)
		return nil, fmt.Errorf("搜索场景失败: %w", err)
	}

	return scenes, nil
}
