package game

import (
	"context"
	"fmt"
	"time"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// CharacterService 角色管理服务
type CharacterService struct {
	db     *gorm.DB
	logger *logger.Logger
}

// NewCharacterService 创建角色管理服务
func NewCharacterService(db *gorm.DB, logger *logger.Logger) *CharacterService {
	return &CharacterService{
		db:     db,
		logger: logger,
	}
}

// CreateCharacter 创建角色
func (s *CharacterService) CreateCharacter(ctx context.Context, worldID uuid.UUID, userID *uuid.UUID, name, description string, characterType string, traits []string) (*models.Character, error) {
	character := &models.Character{
		WorldID:       worldID,
		UserID:        userID,
		Name:          name,
		Description:   &description,
		CharacterType: characterType,
		Traits:        models.StringArray(traits),
		Status:        "active",
		Memories:      models.JSON{"memories": []interface{}{}},
		Experiences:   models.JSON{},
		Relationships: models.JSON{},
	}

	if err := s.db.WithContext(ctx).Create(character).Error; err != nil {
		s.logger.Error("创建角色失败", "error", err, "world_id", worldID, "name", name)
		return nil, fmt.Errorf("创建角色失败: %w", err)
	}

	s.logger.Info("成功创建角色", "character_id", character.ID, "world_id", worldID, "name", name, "type", characterType)
	return character, nil
}

// GetCharacter 获取角色信息
func (s *CharacterService) GetCharacter(ctx context.Context, characterID uuid.UUID) (*models.Character, error) {
	var character models.Character
	err := s.db.WithContext(ctx).
		Preload("World").
		Preload("User").
		Preload("CurrentScene").
		First(&character, characterID).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("角色不存在")
		}
		s.logger.Error("获取角色信息失败", "error", err, "character_id", characterID)
		return nil, fmt.Errorf("获取角色信息失败: %w", err)
	}

	return &character, nil
}

// GetCharactersByWorld 获取世界中的角色列表
func (s *CharacterService) GetCharactersByWorld(ctx context.Context, worldID uuid.UUID, characterType string, limit, offset int) ([]models.Character, int64, error) {
	var characters []models.Character
	var total int64

	query := s.db.WithContext(ctx).Where("world_id = ?", worldID)
	if characterType != "" {
		query = query.Where("character_type = ?", characterType)
	}

	// 获取总数
	if err := query.Model(&models.Character{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取角色总数失败: %w", err)
	}

	// 获取角色列表
	err := query.Preload("User").
		Preload("CurrentScene").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&characters).Error

	if err != nil {
		s.logger.Error("获取世界角色列表失败", "error", err, "world_id", worldID)
		return nil, 0, fmt.Errorf("获取角色列表失败: %w", err)
	}

	return characters, total, nil
}

// GetCharactersByUser 获取用户的角色列表
func (s *CharacterService) GetCharactersByUser(ctx context.Context, userID uuid.UUID, limit, offset int) ([]models.Character, int64, error) {
	var characters []models.Character
	var total int64

	query := s.db.WithContext(ctx).Where("user_id = ?", userID)

	// 获取总数
	if err := query.Model(&models.Character{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取用户角色总数失败: %w", err)
	}

	// 获取角色列表
	err := query.Preload("World").
		Preload("CurrentScene").
		Order("last_action_at DESC NULLS LAST, created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&characters).Error

	if err != nil {
		s.logger.Error("获取用户角色列表失败", "error", err, "user_id", userID)
		return nil, 0, fmt.Errorf("获取用户角色列表失败: %w", err)
	}

	return characters, total, nil
}

// UpdateCharacter 更新角色信息
func (s *CharacterService) UpdateCharacter(ctx context.Context, characterID uuid.UUID, updates map[string]interface{}) error {
	result := s.db.WithContext(ctx).Model(&models.Character{}).Where("id = ?", characterID).Updates(updates)
	if result.Error != nil {
		s.logger.Error("更新角色信息失败", "error", result.Error, "character_id", characterID)
		return fmt.Errorf("更新角色信息失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("角色不存在")
	}

	s.logger.Info("成功更新角色信息", "character_id", characterID, "updates", updates)
	return nil
}

// DeleteCharacter 删除角色
func (s *CharacterService) DeleteCharacter(ctx context.Context, characterID uuid.UUID, userID *uuid.UUID) error {
	query := s.db.WithContext(ctx).Where("id = ?", characterID)
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	result := query.Delete(&models.Character{})
	if result.Error != nil {
		s.logger.Error("删除角色失败", "error", result.Error, "character_id", characterID)
		return fmt.Errorf("删除角色失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("角色不存在或无权限删除")
	}

	s.logger.Info("成功删除角色", "character_id", characterID, "user_id", userID)
	return nil
}

// MoveCharacter 移动角色到指定场景
func (s *CharacterService) MoveCharacter(ctx context.Context, characterID uuid.UUID, sceneID uuid.UUID, userID *uuid.UUID) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取角色信息
		var character models.Character
		query := tx.Where("id = ?", characterID)
		if userID != nil {
			query = query.Where("user_id = ?", *userID)
		}

		if err := query.First(&character).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("角色不存在或无权限操作")
			}
			return fmt.Errorf("获取角色信息失败: %w", err)
		}

		// 检查目标场景是否存在且属于同一世界
		var scene models.Scene
		if err := tx.Where("id = ? AND world_id = ?", sceneID, character.WorldID).First(&scene).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("目标场景不存在或不属于该世界")
			}
			return fmt.Errorf("检查目标场景失败: %w", err)
		}

		// 检查场景是否可以进入
		if !scene.CanEnter() {
			return fmt.Errorf("无法进入该场景")
		}

		// 移动角色
		if err := character.MoveTo(tx, sceneID); err != nil {
			return fmt.Errorf("移动角色失败: %w", err)
		}

		// 更新最后行动时间
		if err := character.UpdateLastAction(tx); err != nil {
			return fmt.Errorf("更新行动时间失败: %w", err)
		}

		s.logger.Info("角色成功移动", "character_id", characterID, "from_scene", character.CurrentSceneID, "to_scene", sceneID)
		return nil
	})
}

// AddCharacterTrait 添加角色特质
func (s *CharacterService) AddCharacterTrait(ctx context.Context, characterID uuid.UUID, trait string, userID *uuid.UUID) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var character models.Character
		query := tx.Where("id = ?", characterID)
		if userID != nil {
			query = query.Where("user_id = ?", *userID)
		}

		if err := query.First(&character).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("角色不存在或无权限操作")
			}
			return fmt.Errorf("获取角色信息失败: %w", err)
		}

		if err := character.AddTrait(tx, trait); err != nil {
			return fmt.Errorf("添加特质失败: %w", err)
		}

		s.logger.Info("成功添加角色特质", "character_id", characterID, "trait", trait)
		return nil
	})
}

// RemoveCharacterTrait 移除角色特质
func (s *CharacterService) RemoveCharacterTrait(ctx context.Context, characterID uuid.UUID, trait string, userID *uuid.UUID) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var character models.Character
		query := tx.Where("id = ?", characterID)
		if userID != nil {
			query = query.Where("user_id = ?", *userID)
		}

		if err := query.First(&character).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("角色不存在或无权限操作")
			}
			return fmt.Errorf("获取角色信息失败: %w", err)
		}

		if err := character.RemoveTrait(tx, trait); err != nil {
			return fmt.Errorf("移除特质失败: %w", err)
		}

		s.logger.Info("成功移除角色特质", "character_id", characterID, "trait", trait)
		return nil
	})
}

// AddCharacterMemory 添加角色记忆
func (s *CharacterService) AddCharacterMemory(ctx context.Context, characterID uuid.UUID, memory models.Memory, userID *uuid.UUID) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var character models.Character
		query := tx.Where("id = ?", characterID)
		if userID != nil {
			query = query.Where("user_id = ?", *userID)
		}

		if err := query.First(&character).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("角色不存在或无权限操作")
			}
			return fmt.Errorf("获取角色信息失败: %w", err)
		}

		if err := character.AddMemory(tx, memory); err != nil {
			return fmt.Errorf("添加记忆失败: %w", err)
		}

		s.logger.Info("成功添加角色记忆", "character_id", characterID, "memory_type", memory.Type)
		return nil
	})
}

// AddCharacterExperience 添加角色阅历
func (s *CharacterService) AddCharacterExperience(ctx context.Context, characterID uuid.UUID, expType, category string, userID *uuid.UUID) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var character models.Character
		query := tx.Where("id = ?", characterID)
		if userID != nil {
			query = query.Where("user_id = ?", *userID)
		}

		if err := query.First(&character).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("角色不存在或无权限操作")
			}
			return fmt.Errorf("获取角色信息失败: %w", err)
		}

		if err := character.AddExperience(tx, expType, category); err != nil {
			return fmt.Errorf("添加阅历失败: %w", err)
		}

		s.logger.Info("成功添加角色阅历", "character_id", characterID, "type", expType, "category", category)
		return nil
	})
}

// UpdateCharacterRelationship 更新角色关系
func (s *CharacterService) UpdateCharacterRelationship(ctx context.Context, characterID uuid.UUID, targetID uuid.UUID, relType string, strengthDelta int, userID *uuid.UUID) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var character models.Character
		query := tx.Where("id = ?", characterID)
		if userID != nil {
			query = query.Where("user_id = ?", *userID)
		}

		if err := query.First(&character).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("角色不存在或无权限操作")
			}
			return fmt.Errorf("获取角色信息失败: %w", err)
		}

		// 检查目标角色是否存在
		var targetCharacter models.Character
		if err := tx.Where("id = ?", targetID).First(&targetCharacter).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return fmt.Errorf("目标角色不存在")
			}
			return fmt.Errorf("检查目标角色失败: %w", err)
		}

		if err := character.UpdateRelationship(tx, targetID, relType, strengthDelta); err != nil {
			return fmt.Errorf("更新关系失败: %w", err)
		}

		s.logger.Info("成功更新角色关系", "character_id", characterID, "target_id", targetID, "type", relType, "delta", strengthDelta)
		return nil
	})
}

// GetCharactersByScene 获取场景中的角色列表
func (s *CharacterService) GetCharactersByScene(ctx context.Context, sceneID uuid.UUID) ([]models.Character, error) {
	var characters []models.Character
	err := s.db.WithContext(ctx).
		Where("current_scene_id = ? AND status = ?", sceneID, "active").
		Preload("User").
		Order("character_type ASC, name ASC").
		Find(&characters).Error

	if err != nil {
		s.logger.Error("获取场景角色列表失败", "error", err, "scene_id", sceneID)
		return nil, fmt.Errorf("获取场景角色列表失败: %w", err)
	}

	return characters, nil
}

// GetNearbyCharacters 获取附近的角色
func (s *CharacterService) GetNearbyCharacters(ctx context.Context, characterID uuid.UUID) ([]models.Character, error) {
	// 首先获取角色当前场景
	character, err := s.GetCharacter(ctx, characterID)
	if err != nil {
		return nil, err
	}

	if character.CurrentSceneID == nil {
		return []models.Character{}, nil
	}

	// 获取同场景的其他角色
	var characters []models.Character
	err = s.db.WithContext(ctx).
		Where("current_scene_id = ? AND id != ? AND status = ?", *character.CurrentSceneID, characterID, "active").
		Preload("User").
		Order("character_type ASC, name ASC").
		Find(&characters).Error

	if err != nil {
		s.logger.Error("获取附近角色失败", "error", err, "character_id", characterID)
		return nil, fmt.Errorf("获取附近角色失败: %w", err)
	}

	return characters, nil
}

// GetCharacterStats 获取角色统计信息
func (s *CharacterService) GetCharacterStats(ctx context.Context, characterID uuid.UUID) (map[string]interface{}, error) {
	character, err := s.GetCharacter(ctx, characterID)
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"character_id":     character.ID,
		"name":             character.Name,
		"type":             character.CharacterType,
		"status":           character.Status,
		"world_id":         character.WorldID,
		"current_scene_id": character.CurrentSceneID,
		"traits_count":     len(character.Traits),
		"memories_count":   len(character.GetMemories()),
		"experiences":      character.GetExperiences(),
		"relationships":    character.GetRelationships(),
		"created_at":       character.CreatedAt,
		"last_action_at":   character.LastActionAt,
	}

	return stats, nil
}
