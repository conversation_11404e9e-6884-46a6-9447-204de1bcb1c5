package game

import (
	"context"
	"testing"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/internal/testutil"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestWorldService_CreateWorld(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	testDB := testutil.SetupTestDB(t)
	defer testDB.CleanupTestDB(t)

	logger := testutil.CreateTestLogger()
	service := NewWorldService(testDB.DB, logger)

	user := testDB.CreateTestUser(t)
	ctx := testutil.SetupTestContext()

	tests := []struct {
		name        string
		request     *CreateWorldRequest
		expectError bool
		description string
	}{
		{
			name: "创建正常世界",
			request: &CreateWorldRequest{
				CreatorID:   user.ID,
				Name:        "测试世界",
				Description: "这是一个测试世界",
				IsPublic:    true,
				MaxPlayers:  10,
				Config:      map[string]interface{}{},
			},
			expectError: false,
			description: "正常的世界创建请求应该成功",
		},
		{
			name: "世界名称为空",
			request: &CreateWorldRequest{
				CreatorID:   user.ID,
				Name:        "",
				Description: "这是一个测试世界",
				IsPublic:    true,
				MaxPlayers:  10,
				Config:      map[string]interface{}{},
			},
			expectError: true,
			description: "世界名称为空应该返回错误",
		},
		{
			name: "最大玩家数为0",
			request: &CreateWorldRequest{
				CreatorID:   user.ID,
				Name:        "测试世界",
				Description: "这是一个测试世界",
				IsPublic:    true,
				MaxPlayers:  0,
				Config:      map[string]interface{}{},
			},
			expectError: true,
			description: "最大玩家数为0应该返回错误",
		},
		{
			name: "创建者ID无效",
			request: &CreateWorldRequest{
				CreatorID:   uuid.New(), // 不存在的用户ID
				Name:        "测试世界",
				Description: "这是一个测试世界",
				IsPublic:    true,
				MaxPlayers:  10,
				Config:      map[string]interface{}{},
			},
			expectError: true,
			description: "无效的创建者ID应该返回错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			world, err := service.CreateWorld(ctx, tt.request)

			if tt.expectError {
				assert.Error(t, err, tt.description)
				assert.Nil(t, world, "错误情况下世界应该为nil")
			} else {
				assert.NoError(t, err, tt.description)
				assert.NotNil(t, world, "成功情况下世界不应该为nil")
				assert.Equal(t, tt.request.Name, world.Name, "世界名称应该匹配")
				assert.Equal(t, tt.request.CreatorID, world.CreatorID, "创建者ID应该匹配")
				assert.Equal(t, "active", world.Status, "新创建的世界状态应该为active")
			}
		})
	}
}

func TestWorldService_GetWorld(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	testDB := testutil.SetupTestDB(t)
	defer testDB.CleanupTestDB(t)

	logger := testutil.CreateTestLogger()
	service := NewWorldService(testDB.DB, logger)

	user := testDB.CreateTestUser(t)
	world := testDB.CreateTestWorld(t, user.ID)
	ctx := testutil.SetupTestContext()

	t.Run("获取存在的世界", func(t *testing.T) {
		result, err := service.GetWorld(ctx, world.ID)

		assert.NoError(t, err, "获取存在的世界不应该返回错误")
		assert.NotNil(t, result, "结果不应该为nil")
		assert.Equal(t, world.ID, result.ID, "世界ID应该匹配")
		assert.Equal(t, world.Name, result.Name, "世界名称应该匹配")
	})

	t.Run("获取不存在的世界", func(t *testing.T) {
		nonExistentID := uuid.New()
		result, err := service.GetWorld(ctx, nonExistentID)

		assert.Error(t, err, "获取不存在的世界应该返回错误")
		assert.Nil(t, result, "结果应该为nil")
	})
}

func TestWorldService_JoinWorld(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	testDB := testutil.SetupTestDB(t)
	defer testDB.CleanupTestDB(t)

	logger := testutil.CreateTestLogger()
	service := NewWorldService(testDB.DB, logger)

	creator := testDB.CreateTestUser(t)
	world := testDB.CreateTestWorld(t, creator.ID)

	// 创建另一个用户来加入世界
	joiner := &models.User{
		ExternalID:       uuid.New().String(),
		ExternalProvider: "test",
		Email:            "<EMAIL>",
		DisplayName:      testutil.StringPtr("Joiner User"),
		GameRoles:        models.StringArray{"user"},
		Status:           "active",
		Preferences:      models.JSON{},
		IDPClaims:        models.JSON{},
	}
	err := testDB.DB.Create(joiner).Error
	require.NoError(t, err)

	ctx := testutil.SetupTestContext()

	t.Run("正常加入世界", func(t *testing.T) {
		err := service.JoinWorld(ctx, world.ID, joiner.ID)
		assert.NoError(t, err, "正常加入世界不应该返回错误")

		// 验证用户确实加入了世界
		var count int64
		err = testDB.DB.Model(&models.Character{}).
			Where("world_id = ? AND user_id = ?", world.ID, joiner.ID).
			Count(&count).Error
		assert.NoError(t, err)
		assert.Greater(t, count, int64(0), "用户应该在世界中有角色")
	})

	t.Run("重复加入世界", func(t *testing.T) {
		err := service.JoinWorld(ctx, world.ID, joiner.ID)
		assert.Error(t, err, "重复加入世界应该返回错误")
	})

	t.Run("加入不存在的世界", func(t *testing.T) {
		nonExistentWorldID := uuid.New()
		err := service.JoinWorld(ctx, nonExistentWorldID, joiner.ID)
		assert.Error(t, err, "加入不存在的世界应该返回错误")
	})
}

func TestWorldService_LeaveWorld(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	testDB := testutil.SetupTestDB(t)
	defer testDB.CleanupTestDB(t)

	logger := testutil.CreateTestLogger()
	service := NewWorldService(testDB.DB, logger)

	creator := testDB.CreateTestUser(t)
	world := testDB.CreateTestWorld(t, creator.ID)

	// 创建用户并加入世界
	user := &models.User{
		ExternalID:       uuid.New().String(),
		ExternalProvider: "test",
		Email:            "<EMAIL>",
		DisplayName:      testutil.StringPtr("Leaver User"),
		GameRoles:        models.StringArray{"user"},
		Status:           "active",
		Preferences:      models.JSON{},
		IDPClaims:        models.JSON{},
	}
	err := testDB.DB.Create(user).Error
	require.NoError(t, err)

	ctx := testutil.SetupTestContext()

	// 先加入世界
	err = service.JoinWorld(ctx, world.ID, user.ID)
	require.NoError(t, err)

	t.Run("正常离开世界", func(t *testing.T) {
		err := service.LeaveWorld(ctx, world.ID, user.ID)
		assert.NoError(t, err, "正常离开世界不应该返回错误")

		// 验证用户确实离开了世界
		var count int64
		err = testDB.DB.Model(&models.Character{}).
			Where("world_id = ? AND user_id = ? AND status = 'active'", world.ID, user.ID).
			Count(&count).Error
		assert.NoError(t, err)
		assert.Equal(t, int64(0), count, "用户应该没有活跃角色在世界中")
	})

	t.Run("离开未加入的世界", func(t *testing.T) {
		anotherUser := &models.User{
			ExternalID:       uuid.New().String(),
			ExternalProvider: "test",
			Email:            "<EMAIL>",
			DisplayName:      testutil.StringPtr("Another User"),
			GameRoles:        models.StringArray{"user"},
			Status:           "active",
			Preferences:      models.JSON{},
			IDPClaims:        models.JSON{},
		}
		err := testDB.DB.Create(anotherUser).Error
		require.NoError(t, err)

		err = service.LeaveWorld(ctx, world.ID, anotherUser.ID)
		assert.Error(t, err, "离开未加入的世界应该返回错误")
	})
}

func TestWorldService_ListWorlds(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	testDB := testutil.SetupTestDB(t)
	defer testDB.CleanupTestDB(t)

	logger := testutil.CreateTestLogger()
	service := NewWorldService(testDB.DB, logger)

	user := testDB.CreateTestUser(t)
	ctx := testutil.SetupTestContext()

	// 创建多个世界
	worlds := make([]*models.World, 3)
	for i := 0; i < 3; i++ {
		world := testDB.CreateTestWorld(t, user.ID)
		worlds[i] = world
	}

	t.Run("列出所有世界", func(t *testing.T) {
		result, err := service.ListWorlds(ctx, &ListWorldsRequest{
			Page:     1,
			PageSize: 10,
			IsPublic: nil,
		})

		assert.NoError(t, err, "列出世界不应该返回错误")
		assert.NotNil(t, result, "结果不应该为nil")
		assert.GreaterOrEqual(t, len(result.Worlds), 3, "应该至少有3个世界")
		assert.GreaterOrEqual(t, result.Total, int64(3), "总数应该至少为3")
	})

	t.Run("只列出公开世界", func(t *testing.T) {
		isPublic := true
		result, err := service.ListWorlds(ctx, &ListWorldsRequest{
			Page:     1,
			PageSize: 10,
			IsPublic: &isPublic,
		})

		assert.NoError(t, err, "列出公开世界不应该返回错误")
		assert.NotNil(t, result, "结果不应该为nil")

		// 验证所有返回的世界都是公开的
		for _, world := range result.Worlds {
			assert.True(t, world.IsPublic, "所有世界都应该是公开的")
		}
	})

	t.Run("分页测试", func(t *testing.T) {
		result, err := service.ListWorlds(ctx, &ListWorldsRequest{
			Page:     1,
			PageSize: 2,
			IsPublic: nil,
		})

		assert.NoError(t, err, "分页查询不应该返回错误")
		assert.NotNil(t, result, "结果不应该为nil")
		assert.LessOrEqual(t, len(result.Worlds), 2, "每页最多应该有2个世界")
	})
}

func TestWorldService_UpdateWorld(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	testDB := testutil.SetupTestDB(t)
	defer testDB.CleanupTestDB(t)

	logger := testutil.CreateTestLogger()
	service := NewWorldService(testDB.DB, logger)

	user := testDB.CreateTestUser(t)
	world := testDB.CreateTestWorld(t, user.ID)
	ctx := testutil.SetupTestContext()

	t.Run("更新世界信息", func(t *testing.T) {
		updateReq := &UpdateWorldRequest{
			Name:        "更新后的世界名称",
			Description: testutil.StringPtr("更新后的世界描述"),
			IsPublic:    testutil.BoolPtr(false),
			MaxPlayers:  testutil.IntPtr(20),
		}

		updatedWorld, err := service.UpdateWorld(ctx, world.ID, updateReq)

		assert.NoError(t, err, "更新世界不应该返回错误")
		assert.NotNil(t, updatedWorld, "更新后的世界不应该为nil")
		assert.Equal(t, updateReq.Name, updatedWorld.Name, "世界名称应该被更新")
		assert.Equal(t, *updateReq.Description, *updatedWorld.Description, "世界描述应该被更新")
		assert.Equal(t, *updateReq.IsPublic, updatedWorld.IsPublic, "公开状态应该被更新")
		assert.Equal(t, *updateReq.MaxPlayers, updatedWorld.MaxPlayers, "最大玩家数应该被更新")
	})

	t.Run("更新不存在的世界", func(t *testing.T) {
		nonExistentID := uuid.New()
		updateReq := &UpdateWorldRequest{
			Name: "更新后的世界名称",
		}

		updatedWorld, err := service.UpdateWorld(ctx, nonExistentID, updateReq)

		assert.Error(t, err, "更新不存在的世界应该返回错误")
		assert.Nil(t, updatedWorld, "结果应该为nil")
	})
}
