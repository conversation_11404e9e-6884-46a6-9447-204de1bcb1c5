package validation

import (
	"context"
	"testing"

	"ai-text-game-iam-npc/internal/testutil"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestValidationService_ValidateContent(t *testing.T) {
	// 跳过数据库测试（如果需要）
	testutil.SkipIfNoDatabase(t)

	// 设置测试数据库
	testDB := testutil.SetupTestDB(t)
	defer testDB.CleanupTestDB(t)

	// 创建测试日志器
	logger := testutil.CreateTestLogger()

	// 创建校验服务
	service := NewValidationService(testDB.DB, logger, nil)

	// 创建测试用户
	user := testDB.CreateTestUser(t)

	tests := []struct {
		name           string
		request        *ValidationRequest
		expectedValid  bool
		expectedErrors int
		description    string
	}{
		{
			name: "正常文本校验通过",
			request: &ValidationRequest{
				UserID:      user.ID,
				ContentType: "character_name",
				Text:        "勇敢的骑士",
				Context:     map[string]interface{}{},
			},
			expectedValid:  true,
			expectedErrors: 0,
			description:    "正常的角色名称应该通过校验",
		},
		{
			name: "包含敏感词的文本",
			request: &ValidationRequest{
				UserID:      user.ID,
				ContentType: "character_description",
				Text:        "这是一个傻逼角色",
				Context:     map[string]interface{}{},
			},
			expectedValid:  false,
			expectedErrors: 1,
			description:    "包含敏感词的文本应该被拒绝",
		},
		{
			name: "SQL注入攻击检测",
			request: &ValidationRequest{
				UserID:      user.ID,
				ContentType: "world_name",
				Text:        "'; DROP TABLE users; --",
				Context:     map[string]interface{}{},
			},
			expectedValid:  false,
			expectedErrors: 1,
			description:    "SQL注入攻击应该被检测并阻止",
		},
		{
			name: "XSS攻击检测",
			request: &ValidationRequest{
				UserID:      user.ID,
				ContentType: "scene_description",
				Text:        "<script>alert('xss')</script>",
				Context:     map[string]interface{}{},
			},
			expectedValid:  false,
			expectedErrors: 1,
			description:    "XSS攻击应该被检测并阻止",
		},
		{
			name: "过长文本检测",
			request: &ValidationRequest{
				UserID:      user.ID,
				ContentType: "description",
				Text:        generateLongText(15000), // 超过10000字符限制
				Context:     map[string]interface{}{},
			},
			expectedValid:  false,
			expectedErrors: 1,
			description:    "过长文本应该被拒绝",
		},
		{
			name: "空文本",
			request: &ValidationRequest{
				UserID:      user.ID,
				ContentType: "character_name",
				Text:        "",
				Context:     map[string]interface{}{},
			},
			expectedValid:  false,
			expectedErrors: 1,
			description:    "空文本应该被拒绝",
		},
		{
			name: "HTML标签过滤",
			request: &ValidationRequest{
				UserID:      user.ID,
				ContentType: "character_name",
				Text:        "<b>勇敢的</b>骑士",
				Context:     map[string]interface{}{},
			},
			expectedValid:  true,
			expectedErrors: 0,
			description:    "HTML标签应该被过滤但文本通过校验",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := testutil.SetupTestContext()

			result, err := service.ValidateContent(ctx, tt.request)

			// 基本断言
			require.NoError(t, err, "校验服务不应该返回错误")
			require.NotNil(t, result, "校验结果不应该为nil")

			// 校验结果断言
			assert.Equal(t, tt.expectedValid, result.IsValid, tt.description)
			assert.Equal(t, tt.expectedErrors, len(result.Errors), "错误数量不匹配")

			// 如果是HTML标签过滤测试，检查过滤后的文本
			if tt.name == "HTML标签过滤" {
				assert.Equal(t, "勇敢的骑士", result.FilteredText, "HTML标签应该被过滤")
			}

			// 检查校验日志是否被记录
			var logCount int64
			err = testDB.DB.Model(&ValidationLog{}).Where("user_id = ?", user.ID).Count(&logCount).Error
			require.NoError(t, err, "查询校验日志失败")
			assert.Greater(t, logCount, int64(0), "应该记录校验日志")
		})
	}
}

func TestValidationService_CheckRateLimit(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	testDB := testutil.SetupTestDB(t)
	defer testDB.CleanupTestDB(t)

	logger := testutil.CreateTestLogger()
	service := NewValidationService(testDB.DB, logger, nil)

	user := testDB.CreateTestUser(t)
	ctx := testutil.SetupTestContext()

	// 测试频率限制
	for i := 0; i < 65; i++ { // 超过每分钟60次的限制
		allowed, err := service.checkRateLimit(ctx, user.ID, "test_endpoint")
		require.NoError(t, err, "检查频率限制失败")

		if i < 60 {
			assert.True(t, allowed, "前60次请求应该被允许")
		} else {
			assert.False(t, allowed, "超过60次的请求应该被拒绝")
		}
	}
}

func TestValidationService_GetValidationStats(t *testing.T) {
	testutil.SkipIfNoDatabase(t)

	testDB := testutil.SetupTestDB(t)
	defer testDB.CleanupTestDB(t)

	logger := testutil.CreateTestLogger()
	service := NewValidationService(testDB.DB, logger, nil)

	user := testDB.CreateTestUser(t)
	ctx := testutil.SetupTestContext()

	// 创建一些校验记录
	validRequests := []*ValidationRequest{
		{
			UserID:      user.ID,
			ContentType: "character_name",
			Text:        "正常角色名",
			Context:     map[string]interface{}{},
		},
		{
			UserID:      user.ID,
			ContentType: "world_name",
			Text:        "正常世界名",
			Context:     map[string]interface{}{},
		},
	}

	invalidRequests := []*ValidationRequest{
		{
			UserID:      user.ID,
			ContentType: "character_name",
			Text:        "傻逼角色",
			Context:     map[string]interface{}{},
		},
	}

	// 执行校验请求
	for _, req := range validRequests {
		_, err := service.ValidateContent(ctx, req)
		require.NoError(t, err)
	}

	for _, req := range invalidRequests {
		_, err := service.ValidateContent(ctx, req)
		require.NoError(t, err)
	}

	// 获取统计信息
	stats, err := service.GetValidationStats(ctx, user.ID, 7)
	require.NoError(t, err, "获取统计信息失败")
	require.NotNil(t, stats, "统计信息不应该为nil")

	// 验证统计信息
	assert.Equal(t, int64(3), stats["total_requests"], "总请求数应该为3")
	assert.Equal(t, int64(2), stats["valid_requests"], "有效请求数应该为2")
	assert.Equal(t, int64(1), stats["invalid_requests"], "无效请求数应该为1")
}

func TestValidationService_FilterProfanity(t *testing.T) {
	logger := testutil.CreateTestLogger()
	service := NewValidationService(nil, logger, nil)

	tests := []struct {
		name           string
		input          string
		expectedOutput string
		expectedFound  bool
		description    string
	}{
		{
			name:           "包含中文敏感词",
			input:          "你是个傻逼",
			expectedOutput: "你是个***",
			expectedFound:  true,
			description:    "中文敏感词应该被过滤",
		},
		{
			name:           "包含英文敏感词",
			input:          "You are a fucking idiot",
			expectedOutput: "You are a *** idiot",
			expectedFound:  true,
			description:    "英文敏感词应该被过滤",
		},
		{
			name:           "正常文本",
			input:          "这是正常的文本内容",
			expectedOutput: "这是正常的文本内容",
			expectedFound:  false,
			description:    "正常文本不应该被过滤",
		},
		{
			name:           "多个敏感词",
			input:          "fuck这个shit真的很damn糟糕",
			expectedOutput: "***这个***真的很***糟糕",
			expectedFound:  true,
			description:    "多个敏感词都应该被过滤",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filtered, found := service.filterProfanity(tt.input)

			assert.Equal(t, tt.expectedOutput, filtered, tt.description)
			assert.Equal(t, tt.expectedFound, found, "敏感词检测结果不匹配")
		})
	}
}

func TestValidationService_CheckSecurity(t *testing.T) {
	logger := testutil.CreateTestLogger()
	service := NewValidationService(nil, logger, nil)

	tests := []struct {
		name        string
		text        string
		expectError bool
		description string
	}{
		{
			name:        "SQL注入检测",
			text:        "'; DROP TABLE users; --",
			expectError: true,
			description: "SQL注入应该被检测",
		},
		{
			name:        "XSS攻击检测",
			text:        "<script>alert('xss')</script>",
			expectError: true,
			description: "XSS攻击应该被检测",
		},
		{
			name:        "正常文本",
			text:        "这是正常的文本内容",
			expectError: false,
			description: "正常文本不应该触发安全检查",
		},
		{
			name:        "包含HTML标签但不是攻击",
			text:        "<p>这是段落</p>",
			expectError: false,
			description: "普通HTML标签不应该触发安全检查",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := service.checkSecurity(tt.text)

			if tt.expectError {
				assert.NotEmpty(t, errors, tt.description)
			} else {
				assert.Empty(t, errors, tt.description)
			}
		})
	}
}

// 辅助函数

// generateLongText 生成指定长度的文本
func generateLongText(length int) string {
	text := ""
	for len(text) < length {
		text += "这是一段很长的文本内容，用于测试文本长度限制功能。"
	}
	return text[:length]
}
