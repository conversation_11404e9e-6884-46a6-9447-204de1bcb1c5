package database

import (
	"database/sql"
	"fmt"
	"time"

	"ai-text-game-iam-npc/internal/config"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// DB 数据库连接实例
var DB *gorm.DB

// New 创建新的数据库连接
func New(cfg *config.DatabaseConfig) (*gorm.DB, error) {
	// 配置GORM日志级别
	var logLevel logger.LogLevel
	switch cfg.SSLMode {
	case "development":
		logLevel = logger.Info
	default:
		logLevel = logger.Error
	}

	// 连接数据库
	db, err := gorm.Open(postgres.Open(cfg.DSN()), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取底层的sql.DB实例
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库实例失败: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.MaxLifetime)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}

	DB = db
	return db, nil
}

// Close 关闭数据库连接
func Close(db *gorm.DB) error {
	if db == nil {
		return nil
	}

	sqlDB, err := db.DB()
	if err != nil {
		return err
	}

	return sqlDB.Close()
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}

// Transaction 执行事务
func Transaction(db *gorm.DB, fn func(*gorm.DB) error) error {
	return db.Transaction(fn)
}

// IsRecordNotFound 检查是否为记录不存在错误
func IsRecordNotFound(err error) bool {
	return err == gorm.ErrRecordNotFound
}

// IsDuplicateKey 检查是否为重复键错误
func IsDuplicateKey(err error) bool {
	if err == nil {
		return false
	}
	// PostgreSQL重复键错误代码
	return err.Error() == "23505"
}

// Paginate 分页查询
func Paginate(page, pageSize int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if page <= 0 {
			page = 1
		}

		switch {
		case pageSize > 100:
			pageSize = 100
		case pageSize <= 0:
			pageSize = 10
		}

		offset := (page - 1) * pageSize
		return db.Offset(offset).Limit(pageSize)
	}
}

// Count 计算总数
func Count(db *gorm.DB, model interface{}) (int64, error) {
	var count int64
	err := db.Model(model).Count(&count).Error
	return count, err
}

// Exists 检查记录是否存在
func Exists(db *gorm.DB, model interface{}, conditions ...interface{}) (bool, error) {
	var count int64
	err := db.Model(model).Where(conditions[0], conditions[1:]...).Count(&count).Error
	return count > 0, err
}

// CreateInBatches 批量创建记录
func CreateInBatches(db *gorm.DB, value interface{}, batchSize int) error {
	return db.CreateInBatches(value, batchSize).Error
}

// SoftDelete 软删除记录
func SoftDelete(db *gorm.DB, model interface{}, id interface{}) error {
	return db.Delete(model, id).Error
}

// HardDelete 硬删除记录
func HardDelete(db *gorm.DB, model interface{}, id interface{}) error {
	return db.Unscoped().Delete(model, id).Error
}

// GetStats 获取数据库统计信息
func GetStats(db *gorm.DB) (*sql.DBStats, error) {
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}
	stats := sqlDB.Stats()
	return &stats, nil
}
