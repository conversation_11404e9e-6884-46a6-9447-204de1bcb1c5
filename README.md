# AI-Text-Game-IAM-NPC (AI文本游戏-我是NPC)

这是一款由AI驱动的文本冒险游戏。在这个游戏中，世界、场景、事件、NPC和物品都是由大型语言模型根据玩家提供的初始世界描述动态生成的。

## 核心特性

1.  **动态世界生成**: 玩家提供游戏世界的描述，AI会生成一个结构化的游戏环境。
2.  **结构化内容**: 游戏世界，包括地图、场景、事件、NPC和物品，都会被生成为结构化的JSON数据。
3.  **查看与分享**: 生成的世界可以被保存、查看，并分享给其他玩家。
4.  **游戏时间系统**: 游戏拥有自己内部的时钟，其速度可以配置。玩家的行动会消耗游戏内的时间。
5.  **战争迷雾与探索**: 玩家的视野有限，必须通过探索地图来揭开新的区域。
6.  **基于时间的行动**: 诸如探索、移动、与事件互动以及和NPC交谈等行动，都会消耗游戏内的时间。

## 技术栈 (建议)

*   **前端**: React / Vue.js
*   **后端**: Python (FastAPI) / Node.js (Express)
*   **数据格式**: JSON