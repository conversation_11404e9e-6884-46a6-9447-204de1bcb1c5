# AI文本游戏 "I am NPC" 后端系统

一个基于AI驱动的文本冒险游戏后端系统，玩家可以在动态生成的世界中与AI角色互动，体验沉浸式的文本冒险。

## 🎮 项目概述

"I am NPC" 是一个创新的AI文本游戏，其中：
- **玩家**可以创建角色，探索世界，与环境和其他角色互动
- **AI系统**动态生成游戏内容，包括场景描述、角色对话和事件
- **世界**是持久化的，支持多玩家同时在线
- **内容校验**确保游戏内容的安全性和适宜性

## 🏗️ 技术架构

### 核心技术栈
- **后端语言**: Go 1.19+
- **Web框架**: Gin
- **数据库**: PostgreSQL 13+
- **缓存**: Redis 6+
- **认证**: OAuth2/OIDC (支持Google、GitHub等)
- **AI集成**: 支持多种AI提供商API

### 架构特点
- **微服务架构**: 模块化设计，易于扩展
- **RESTful API**: 标准化的API接口
- **事件驱动**: 异步事件处理机制
- **内容校验**: 多层次的内容安全检查
- **测试覆盖**: 完整的单元测试和集成测试

## 📁 项目结构

```
ai-text-game-iam-npc/
├── cmd/
│   └── server/                 # 主服务器入口
├── internal/                   # 内部包（不对外暴露）
│   ├── auth/                  # 认证服务
│   ├── ai/                    # AI集成服务
│   ├── config/                # 配置管理
│   ├── game/                  # 游戏核心逻辑
│   ├── handlers/              # HTTP处理器
│   ├── models/                # 数据模型
│   ├── routes/                # 路由配置
│   ├── validation/            # 内容校验
│   ├── migration/             # 数据库迁移
│   └── testutil/              # 测试工具
├── pkg/                       # 公共包
│   ├── database/              # 数据库连接
│   └── logger/                # 日志系统
├── migrations/                # 数据库迁移文件
├── scripts/                   # 脚本文件
└── docs/                      # 文档
```

## 🚀 快速开始

### 环境要求
- Go 1.19+
- PostgreSQL 13+
- Redis 6+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd ai-text-game-iam-npc
```

2. **安装依赖**
```bash
go mod download
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和其他服务
```

4. **数据库迁移**
```bash
# 创建数据库
createdb ai_text_game

# 运行迁移
go run cmd/migrate/main.go up
```

5. **启动服务**
```bash
go run cmd/server/main.go
```

服务将在 `http://localhost:8080` 启动。

## 🔧 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `SERVER_PORT` | 服务器端口 | 8080 |
| `DB_HOST` | 数据库主机 | localhost |
| `DB_PORT` | 数据库端口 | 5432 |
| `DB_USER` | 数据库用户 | postgres |
| `DB_PASSWORD` | 数据库密码 | |
| `DB_NAME` | 数据库名称 | ai_text_game |
| `REDIS_HOST` | Redis主机 | localhost |
| `REDIS_PORT` | Redis端口 | 6379 |
| `JWT_SECRET` | JWT密钥 | |
| `AI_PROVIDER` | AI提供商 | mock |

### OAuth配置

支持多种OAuth提供商：

```bash
# Google OAuth
OAUTH_GOOGLE_CLIENT_ID=your_client_id
OAUTH_GOOGLE_CLIENT_SECRET=your_client_secret

# GitHub OAuth
OAUTH_GITHUB_CLIENT_ID=your_client_id
OAUTH_GITHUB_CLIENT_SECRET=your_client_secret
```

## 📚 API文档

### 认证相关
- `GET /api/v1/auth/login/:provider` - OAuth登录
- `GET /api/v1/auth/callback/:provider` - OAuth回调
- `POST /api/v1/auth/refresh` - 刷新Token

### 游戏世界
- `POST /api/v1/game/worlds` - 创建世界
- `GET /api/v1/game/worlds` - 列出世界
- `GET /api/v1/game/worlds/:id` - 获取世界详情
- `PUT /api/v1/game/worlds/:id` - 更新世界
- `POST /api/v1/game/worlds/:id/join` - 加入世界
- `POST /api/v1/game/worlds/:id/leave` - 离开世界

### 角色管理
- `POST /api/v1/game/characters` - 创建角色
- `GET /api/v1/game/characters/:id` - 获取角色
- `PUT /api/v1/game/characters/:id` - 更新角色
- `GET /api/v1/game/worlds/:world_id/characters` - 列出世界中的角色

### 场景系统
- `POST /api/v1/game/scenes` - 创建场景
- `GET /api/v1/game/scenes/:id` - 获取场景
- `PUT /api/v1/game/scenes/:id` - 更新场景
- `GET /api/v1/game/worlds/:world_id/scenes` - 列出世界中的场景

### AI生成
- `POST /api/v1/ai/generate` - 生成内容
- `POST /api/v1/ai/generate/scene` - 生成场景
- `POST /api/v1/ai/generate/character` - 生成角色
- `GET /api/v1/ai/history` - 获取AI交互历史

### 内容校验
- `POST /api/v1/validation/validate` - 校验内容
- `POST /api/v1/validation/batch-validate` - 批量校验
- `GET /api/v1/validation/stats` - 获取校验统计
- `GET /api/v1/validation/config` - 获取校验配置

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
./scripts/run_tests.sh

# 只运行单元测试
./scripts/run_tests.sh --unit

# 跳过数据库测试
./scripts/run_tests.sh --skip-db

# 生成覆盖率报告
./scripts/run_tests.sh --coverage
```

### 测试环境配置
```bash
# 测试数据库配置
export TEST_DB_HOST=localhost
export TEST_DB_PORT=5432
export TEST_DB_USER=postgres
export TEST_DB_PASSWORD=
export TEST_DB_NAME=ai_text_game_test
```

## 🔒 安全特性

### 内容校验系统
- **敏感词过滤**: 自动检测和过滤不当内容
- **SQL注入防护**: 检测和阻止SQL注入攻击
- **XSS防护**: 过滤恶意脚本内容
- **频率限制**: 防止API滥用
- **AI内容审核**: 智能内容审核（可选）

### 认证授权
- **OAuth2集成**: 支持主流OAuth提供商
- **JWT Token**: 安全的会话管理
- **角色权限**: 基于角色的访问控制
- **API限流**: 防止恶意请求

## 🚀 部署

### Docker部署
```bash
# 构建镜像
docker build -t ai-text-game .

# 运行容器
docker run -p 8080:8080 ai-text-game
```

### 生产环境配置
- 使用环境变量管理敏感配置
- 配置反向代理（Nginx）
- 设置SSL证书
- 配置监控和日志收集

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目链接: [GitHub Repository](https://github.com/your-username/ai-text-game-iam-npc)
- 问题反馈: [Issues](https://github.com/your-username/ai-text-game-iam-npc/issues)

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和测试者！

## 📋 开发状态

### 已完成功能
- ✅ 项目初始化和配置管理
- ✅ 数据库设计和迁移系统
- ✅ 用户认证和OAuth集成
- ✅ 游戏核心服务（世界、角色、场景、事件）
- ✅ AI集成服务（支持模拟模式）
- ✅ 内容校验和安全系统
- ✅ RESTful API接口
- ✅ 单元测试和集成测试
- ✅ 项目文档

### 技术亮点
- **模块化架构**: 清晰的代码组织和依赖管理
- **安全优先**: 多层次的内容校验和安全防护
- **测试驱动**: 完整的测试覆盖和质量保证
- **可扩展性**: 支持多种AI提供商和认证方式
- **生产就绪**: 完整的配置管理和部署支持