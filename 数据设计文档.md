# AI文本游戏数据设计文档

## 1. 数据架构概述

### 1.1. 数据分层设计
```
┌─────────────────────────────────────────────────────────────┐
│                    界面层 (Presentation Layer)                │
│  前端组件状态、UI数据模型、表单验证数据                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    传输层 (Transport Layer)                  │
│  API请求/响应数据、WebSocket消息、HTTP状态码                  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    业务层 (Business Layer)                   │
│  游戏逻辑数据模型、业务规则、数据转换                         │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    缓存层 (Cache Layer)                      │
│  Redis缓存、会话数据、临时状态、队列数据                      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    存储层 (Storage Layer)                    │
│  PostgreSQL数据库、文件存储、备份数据                        │
└─────────────────────────────────────────────────────────────┘
```

### 1.2. 数据一致性策略
- **强一致性**: 用户账户、游戏核心状态
- **最终一致性**: 统计数据、日志记录
- **会话一致性**: 用户界面状态、临时数据

## 2. 存储层数据设计

### 2.1. 核心数据表结构

#### 用户相关表

```sql
-- 用户基础信息表 (外部IDP集成)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    external_id VARCHAR(255) UNIQUE NOT NULL,  -- 外部IDP用户ID
    external_provider VARCHAR(50) NOT NULL,    -- IDP提供商 (auth0, keycloak, etc.)
    email VARCHAR(255) NOT NULL,               -- 从IDP获取的邮箱
    display_name VARCHAR(100),                 -- 显示名称
    avatar_url TEXT,                           -- 头像URL
    game_roles JSONB DEFAULT '["user"]',       -- 游戏内角色 ["user", "premium", "admin", "developer"]
    status VARCHAR(20) DEFAULT 'active',       -- active, suspended, deleted
    preferences JSONB DEFAULT '{}',            -- 用户偏好设置
    idp_claims JSONB DEFAULT '{}',             -- 从IDP获取的额外声明
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,

    -- 索引
    CONSTRAINT unique_external_user UNIQUE (external_id, external_provider)
);

-- 用户统计信息表
CREATE TABLE user_stats (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    total_play_time INTEGER DEFAULT 0, -- 总游戏时间(分钟)
    worlds_created INTEGER DEFAULT 0, -- 创建的世界数量
    worlds_joined INTEGER DEFAULT 0, -- 加入的世界数量
    achievements JSONB DEFAULT '[]', -- 成就列表
    level INTEGER DEFAULT 1, -- 用户等级
    experience INTEGER DEFAULT 0, -- 经验值
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 游戏世界相关表

```sql
-- 游戏世界表
CREATE TABLE worlds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    creator_id UUID NOT NULL REFERENCES users(id),
    world_config JSONB NOT NULL, -- 世界配置(时间倍率、规则等)
    world_state JSONB NOT NULL, -- 当前世界状态
    status VARCHAR(20) DEFAULT 'active', -- active, paused, archived
    is_public BOOLEAN DEFAULT false, -- 是否公开
    max_players INTEGER DEFAULT 10, -- 最大玩家数
    current_players INTEGER DEFAULT 0, -- 当前玩家数
    game_time BIGINT DEFAULT 0, -- 游戏内时间(分钟)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 场景表
CREATE TABLE scenes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    scene_type VARCHAR(50), -- indoor, outdoor, dungeon, etc.
    properties JSONB DEFAULT '{}', -- 场景属性和特质
    connections JSONB DEFAULT '[]', -- 连接的场景列表
    entities_present JSONB DEFAULT '[]', -- 当前场景中的实体ID列表
    environment JSONB DEFAULT '{}', -- 环境信息(天气、光照等)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 角色表(包括玩家角色和NPC)
CREATE TABLE characters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id), -- NULL表示NPC
    name VARCHAR(100) NOT NULL,
    description TEXT,
    character_type VARCHAR(20) NOT NULL, -- player, npc, collective
    current_scene_id UUID REFERENCES scenes(id),
    traits JSONB DEFAULT '[]', -- 特质列表
    inventory JSONB DEFAULT '[]', -- 物品清单
    memories JSONB DEFAULT '[]', -- 记忆系统
    experiences JSONB DEFAULT '[]', -- 阅历系统
    emotions JSONB DEFAULT '{}', -- 情感状态
    goals JSONB DEFAULT '[]', -- 目标列表
    relationships JSONB DEFAULT '{}', -- 与其他角色的关系
    stats JSONB DEFAULT '{}', -- 统计信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 角色记忆详细表
CREATE TABLE character_memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    memory_type VARCHAR(50) NOT NULL, -- event, person, location, item, knowledge
    content TEXT NOT NULL,
    importance_score FLOAT DEFAULT 0.5, -- 重要性评分 0-1
    emotional_impact FLOAT DEFAULT 0.0, -- 情感影响 -1到1
    associated_entities JSONB DEFAULT '[]', -- 关联的实体ID
    tags JSONB DEFAULT '[]', -- 记忆标签
    decay_rate FLOAT DEFAULT 0.1, -- 遗忘速率
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 角色阅历详细表
CREATE TABLE character_experiences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    experience_category VARCHAR(50) NOT NULL, -- combat, social, exploration, crafting, event
    experience_type VARCHAR(100) NOT NULL, -- 具体阅历类型
    description TEXT NOT NULL,
    proficiency_level INTEGER DEFAULT 1, -- 熟练度等级
    success_count INTEGER DEFAULT 0, -- 成功次数
    failure_count INTEGER DEFAULT 0, -- 失败次数
    related_skills JSONB DEFAULT '[]', -- 相关技能
    applicable_contexts JSONB DEFAULT '[]', -- 适用场景
    acquired_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 游戏内容相关表

```sql
-- 实体表(物品、事件等)
CREATE TABLE entities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    entity_type VARCHAR(50) NOT NULL, -- item, event, goal, weather, etc.
    name VARCHAR(200) NOT NULL,
    description TEXT,
    properties JSONB DEFAULT '{}', -- 实体属性
    traits JSONB DEFAULT '[]', -- 特质列表
    location_type VARCHAR(20), -- scene, character, inventory
    location_id UUID, -- 所在位置的ID
    status VARCHAR(20) DEFAULT 'active', -- active, consumed, destroyed
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 游戏事件日志表
CREATE TABLE game_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL, -- action, evolution, system
    actor_id UUID, -- 触发者ID(角色或系统)
    target_id UUID, -- 目标ID
    scene_id UUID REFERENCES scenes(id),
    event_data JSONB NOT NULL, -- 事件详细数据
    narrative_text TEXT, -- 叙事文本
    game_time BIGINT NOT NULL, -- 事件发生的游戏时间
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI交互日志表
CREATE TABLE ai_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID REFERENCES worlds(id),
    user_id UUID REFERENCES users(id),
    interaction_type VARCHAR(50) NOT NULL, -- world_gen, action, evolution, etc.
    prompt_text TEXT NOT NULL,
    response_text TEXT,
    token_usage INTEGER,
    response_time INTEGER, -- 响应时间(毫秒)
    status VARCHAR(20) DEFAULT 'success', -- success, error, timeout
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 内容安全审核表
CREATE TABLE content_audits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_type VARCHAR(50) NOT NULL, -- user_input, ai_output, system_message
    content_source VARCHAR(100), -- 内容来源标识
    original_content TEXT NOT NULL,
    processed_content TEXT, -- 处理后的内容
    audit_result JSONB NOT NULL, -- 审核结果详情
    safety_score FLOAT, -- 安全评分 0-1
    consistency_score FLOAT, -- 一致性评分 0-1
    quality_score FLOAT, -- 质量评分 0-1
    violations JSONB DEFAULT '[]', -- 违规项列表
    action_taken VARCHAR(50), -- block, warn, approve, modify
    reviewer_id UUID REFERENCES users(id), -- 人工审核员ID
    review_notes TEXT, -- 审核备注
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE
);

-- 世界规模框架表
CREATE TABLE world_frameworks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    framework_data JSONB NOT NULL, -- 世界规模框架数据
    regions JSONB DEFAULT '[]', -- 主要地理区域
    landmarks JSONB DEFAULT '[]', -- 重要地标
    distances JSONB DEFAULT '{}', -- 距离关系
    boundaries JSONB DEFAULT '{}', -- 世界边界
    player_capacity INTEGER DEFAULT 10, -- 玩家容量
    convergence_points JSONB DEFAULT '[]', -- 汇聚点设计
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 玩家初始化分配表
CREATE TABLE player_allocations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    world_id UUID NOT NULL REFERENCES worlds(id) ON DELETE CASCADE,
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    allocated_region VARCHAR(100) NOT NULL, -- 分配的区域
    start_position JSONB NOT NULL, -- 起始位置坐标
    distance_to_others JSONB DEFAULT '{}', -- 与其他玩家的距离
    convergence_path JSONB DEFAULT '[]', -- 预设的汇聚路径
    allocation_strategy VARCHAR(50), -- 分配策略
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2.2. 索引设计

```sql
-- 性能优化索引
CREATE INDEX idx_worlds_creator ON worlds(creator_id);
CREATE INDEX idx_worlds_status ON worlds(status) WHERE status = 'active';
CREATE INDEX idx_scenes_world ON scenes(world_id);
CREATE INDEX idx_characters_world ON characters(world_id);
CREATE INDEX idx_characters_user ON characters(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX idx_characters_scene ON characters(current_scene_id);
CREATE INDEX idx_entities_world ON entities(world_id);
CREATE INDEX idx_entities_location ON entities(location_type, location_id);
CREATE INDEX idx_game_events_world_time ON game_events(world_id, game_time);
CREATE INDEX idx_ai_interactions_created ON ai_interactions(created_at);

-- 记忆和阅历系统索引
CREATE INDEX idx_character_memories_character ON character_memories(character_id);
CREATE INDEX idx_character_memories_type ON character_memories(memory_type);
CREATE INDEX idx_character_memories_importance ON character_memories(importance_score DESC);
CREATE INDEX idx_character_experiences_character ON character_experiences(character_id);
CREATE INDEX idx_character_experiences_category ON character_experiences(experience_category);
CREATE INDEX idx_character_experiences_proficiency ON character_experiences(proficiency_level DESC);

-- 内容安全索引
CREATE INDEX idx_content_audits_type ON content_audits(content_type);
CREATE INDEX idx_content_audits_created ON content_audits(created_at);
CREATE INDEX idx_content_audits_safety_score ON content_audits(safety_score);
CREATE INDEX idx_content_audits_action ON content_audits(action_taken);

-- 世界框架和玩家分配索引
CREATE INDEX idx_world_frameworks_world ON world_frameworks(world_id);
CREATE INDEX idx_player_allocations_world ON player_allocations(world_id);
CREATE INDEX idx_player_allocations_character ON player_allocations(character_id);

-- 复合索引
CREATE INDEX idx_game_events_world_type_time ON game_events(world_id, event_type, game_time);
CREATE INDEX idx_entities_world_type_status ON entities(world_id, entity_type, status);
CREATE INDEX idx_character_memories_character_type ON character_memories(character_id, memory_type);
CREATE INDEX idx_character_experiences_character_category ON character_experiences(character_id, experience_category);
```

## 3. 缓存层数据设计

### 3.1. Redis数据结构设计

#### 会话管理
```
# 用户会话
user:session:{user_id} -> {
    "token": "jwt_token",
    "refresh_token": "refresh_token", 
    "expires_at": timestamp,
    "current_world_id": "world_uuid"
}

# 在线用户集合
online_users -> Set[user_id]
```

#### 游戏状态缓存
```
# 世界实时状态
world:state:{world_id} -> {
    "current_time": game_time,
    "active_players": [user_ids],
    "pending_actions": [action_objects],
    "last_evolution": timestamp
}

# 场景快照
scene:snapshot:{scene_id} -> {
    "entities": [entity_ids],
    "characters": [character_ids],
    "last_updated": timestamp
}

# 角色状态缓存
character:state:{character_id} -> {
    "current_scene": scene_id,
    "recent_actions": [actions],
    "active_memories": [memories]
}
```

#### 队列系统
```
# AI处理队列
queue:ai_requests -> List[{
    "request_id": uuid,
    "world_id": uuid,
    "type": "action|evolution|generation",
    "priority": number,
    "data": object,
    "created_at": timestamp
}]

# 通知队列
queue:notifications -> List[{
    "user_id": uuid,
    "type": "game_update|system",
    "message": string,
    "data": object
}]
```

### 3.2. 缓存策略

#### 缓存更新策略
- **Write-Through**: 用户信息、世界配置等重要数据
- **Write-Behind**: 游戏事件日志、统计数据
- **Cache-Aside**: 场景数据、角色状态等频繁变化的数据

#### 缓存失效策略
- **TTL**: 临时状态数据(5-30分钟)
- **主动失效**: 关键数据变更时主动清除相关缓存
- **版本控制**: 使用版本号避免缓存不一致

## 4. 传输层数据设计

### 4.1. API数据格式

#### 标准响应格式
```json
{
    "success": true,
    "data": {},
    "message": "操作成功",
    "code": 200,
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid"
}
```

#### 错误响应格式
```json
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "输入数据验证失败",
        "details": {
            "field": "username",
            "reason": "用户名已存在"
        }
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid"
}
```

### 4.2. WebSocket消息格式

#### 游戏状态更新
```json
{
    "type": "game_update",
    "world_id": "uuid",
    "data": {
        "narrative": "你看到一个神秘的陌生人走向你...",
        "scene_changes": {},
        "character_changes": {},
        "new_entities": []
    },
    "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 实时通知
```json
{
    "type": "notification",
    "category": "system|game|social",
    "title": "系统通知",
    "message": "你的世界有新的访客",
    "data": {},
    "timestamp": "2024-01-01T00:00:00Z"
}
```

## 5. 界面层数据设计

### 5.1. 前端状态管理

#### 全局状态结构
```typescript
interface AppState {
    user: UserState;
    game: GameState;
    ui: UIState;
    cache: CacheState;
}

interface UserState {
    profile: UserProfile | null;
    preferences: UserPreferences;
    isAuthenticated: boolean;
    permissions: string[];
}

interface GameState {
    currentWorld: WorldState | null;
    character: CharacterState | null;
    scene: SceneState | null;
    inventory: InventoryState;
    history: GameEvent[];
}

interface UIState {
    loading: boolean;
    activePanel: string;
    notifications: Notification[];
    modals: ModalState[];
}
```

### 5.2. 组件数据模型

#### 游戏主界面数据
```typescript
interface GameMainProps {
    narrative: NarrativeData;
    character: CharacterData;
    scene: SceneData;
    availableActions: ActionData[];
    onAction: (action: string) => void;
}

interface NarrativeData {
    text: string;
    timestamp: string;
    type: 'story' | 'action' | 'system';
    entities: EntityReference[];
}
```

## 6. 数据安全与隐私

### 6.1. 敏感数据处理
- **密码**: 使用bcrypt加密存储
- **个人信息**: 支持数据脱敏和匿名化
- **游戏内容**: 内容审核和过滤机制

### 6.2. 数据备份策略
- **实时备份**: 关键数据的实时同步备份
- **定期备份**: 每日全量备份和增量备份
- **异地备份**: 多地域数据备份保障

### 6.3. 数据访问控制
- **行级安全**: 基于用户权限的数据访问控制
- **字段级加密**: 敏感字段的透明加密
- **审计日志**: 完整的数据访问和修改日志

## 7. 数据库迁移系统设计

### 7.1. 迁移表结构
```sql
-- 数据库迁移版本管理表
CREATE TABLE schema_migrations (
    version BIGINT PRIMARY KEY,           -- 迁移版本号 (时间戳格式: 20240101120000)
    filename VARCHAR(255) NOT NULL,       -- 迁移文件名
    checksum VARCHAR(64) NOT NULL,        -- 文件校验和
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    execution_time INTEGER,               -- 执行时间(毫秒)
    success BOOLEAN DEFAULT TRUE,         -- 执行是否成功
    error_message TEXT                    -- 错误信息
);

-- 迁移锁表 (防止并发执行)
CREATE TABLE migration_lock (
    id INTEGER PRIMARY KEY DEFAULT 1,
    locked BOOLEAN DEFAULT FALSE,
    locked_at TIMESTAMP WITH TIME ZONE,
    locked_by VARCHAR(255),              -- 执行迁移的实例ID
    CONSTRAINT single_lock CHECK (id = 1)
);
```

### 7.2. Go语言迁移工具设计
```go
// 迁移接口定义
type Migration interface {
    Up() error      // 执行迁移
    Down() error    // 回滚迁移
    Version() int64 // 获取版本号
    Description() string // 获取描述
}

// 迁移管理器
type MigrationManager struct {
    db          *sql.DB
    migrations  []Migration
    lockTimeout time.Duration
}

// 迁移执行结果
type MigrationResult struct {
    Version       int64         `json:"version"`
    Filename      string        `json:"filename"`
    ExecutionTime time.Duration `json:"execution_time"`
    Success       bool          `json:"success"`
    Error         string        `json:"error,omitempty"`
}
```

### 7.3. 迁移脚本示例
```go
// migrations/20240101120000_add_external_auth.go
package migrations

import (
    "database/sql"
    "fmt"
)

type Migration20240101120000 struct{}

func (m *Migration20240101120000) Version() int64 {
    return 20240101120000
}

func (m *Migration20240101120000) Description() string {
    return "添加外部认证支持"
}

func (m *Migration20240101120000) Up() error {
    queries := []string{
        `ALTER TABLE users ADD COLUMN external_id VARCHAR(255)`,
        `ALTER TABLE users ADD COLUMN external_provider VARCHAR(50)`,
        `CREATE UNIQUE INDEX idx_users_external ON users(external_id, external_provider)`,
    }

    for _, query := range queries {
        if _, err := db.Exec(query); err != nil {
            return fmt.Errorf("执行迁移失败: %w", err)
        }
    }
    return nil
}

func (m *Migration20240101120000) Down() error {
    queries := []string{
        `DROP INDEX IF EXISTS idx_users_external`,
        `ALTER TABLE users DROP COLUMN IF EXISTS external_provider`,
        `ALTER TABLE users DROP COLUMN IF EXISTS external_id`,
    }

    for _, query := range queries {
        if _, err := db.Exec(query); err != nil {
            return fmt.Errorf("回滚迁移失败: %w", err)
        }
    }
    return nil
}
```

### 7.4. 迁移执行流程
1. **获取迁移锁**: 防止并发执行
2. **检查当前版本**: 从schema_migrations表获取最新版本
3. **收集待执行迁移**: 找出所有未执行的迁移
4. **验证迁移文件**: 检查文件完整性和依赖关系
5. **执行迁移**: 按版本顺序执行迁移
6. **记录执行结果**: 更新schema_migrations表
7. **释放迁移锁**: 完成后释放锁

### 7.5. 迁移最佳实践
- **版本命名**: 使用时间戳格式确保唯一性和顺序性
- **原子性**: 每个迁移在事务中执行，确保原子性
- **幂等性**: 迁移脚本应该是幂等的，可重复执行
- **向后兼容**: 尽量保持向后兼容，避免破坏性变更
- **测试验证**: 在测试环境充分验证后再应用到生产环境

## 8. Go语言数据结构设计

### 8.1. 核心数据模型
```go
// 用户模型
type User struct {
    ID               string            `json:"id" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
    ExternalID       string            `json:"external_id" gorm:"uniqueIndex:idx_external_user"`
    ExternalProvider string            `json:"external_provider" gorm:"uniqueIndex:idx_external_user"`
    Email            string            `json:"email" gorm:"not null"`
    DisplayName      string            `json:"display_name"`
    AvatarURL        string            `json:"avatar_url"`
    GameRoles        pq.StringArray    `json:"game_roles" gorm:"type:text[]"`
    Status           string            `json:"status" gorm:"default:active"`
    Preferences      datatypes.JSON    `json:"preferences" gorm:"type:jsonb"`
    IDPClaims        datatypes.JSON    `json:"idp_claims" gorm:"type:jsonb"`
    CreatedAt        time.Time         `json:"created_at"`
    UpdatedAt        time.Time         `json:"updated_at"`
    LastLoginAt      *time.Time        `json:"last_login_at"`
}

// 游戏世界模型
type World struct {
    ID          string         `json:"id" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
    Name        string         `json:"name" gorm:"not null"`
    Description string         `json:"description"`
    CreatorID   string         `json:"creator_id" gorm:"not null"`
    Settings    datatypes.JSON `json:"settings" gorm:"type:jsonb"`
    Status      string         `json:"status" gorm:"default:active"`
    IsPublic    bool           `json:"is_public" gorm:"default:false"`
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`

    // 关联关系
    Creator     User           `json:"creator" gorm:"foreignKey:CreatorID"`
    Scenes      []Scene        `json:"scenes,omitempty"`
    Characters  []Character    `json:"characters,omitempty"`
}
```

本数据设计确保了系统的数据一致性、性能和安全性，同时支持复杂的游戏逻辑、AI集成需求和数据库版本管理。
